"""
快代理配置对话框 - 可复用版本
基于原项目的代理配置界面，适配快代理SDK
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit,
                             QPushButton, QLabel, QCheckBox, QMessageBox,
                             QProgressBar, QWidget, QGroupBox, QLineEdit, 
                             QSpinBox, QFormLayout)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

from ..client import KuaidailiClient
from ..config import KuaidailiConfig
from ..exceptions import APIException, ConfigException


class ProxyDialog(QDialog):
    """快代理配置对话框"""

    def __init__(self, parent=None, config=None):
        super().__init__(parent)
        
        # 配置管理
        if config:
            self.config = config
        else:
            self.config = KuaidailiConfig()
        
        self.client = None
        self.init_ui()
        self.load_config()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("快代理配置")
        self.setModal(True)
        self.resize(600, 500)

        # 创建主布局
        layout = QVBoxLayout(self)

        # 重要提示
        warning_label = QLabel("⚠️ 重要提示：建议配置代理以提高稳定性！")
        warning_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Bold))
        warning_label.setStyleSheet("""
            QLabel {
                color: #dc3545;
                background-color: #f8d7da;
                border: 2px solid #f5c6cb;
                border-radius: 6px;
                padding: 10px;
                margin: 5px 0;
            }
        """)
        layout.addWidget(warning_label)

        # 启用代理复选框
        self.cb_enable_proxy = QCheckBox("启用快代理")
        self.cb_enable_proxy.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        self.cb_enable_proxy.setStyleSheet("""
            QCheckBox {
                color: #007bff;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.cb_enable_proxy)

        # 快代理配置界面
        kuaidaili_widget = self.create_kuaidaili_widget()
        layout.addWidget(kuaidaili_widget)

        # 按钮区域
        button_layout = QHBoxLayout()
        self.btn_save = QPushButton("保存配置")
        self.btn_cancel = QPushButton("取消")
        
        # 按钮样式
        self.btn_save.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.btn_cancel.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(self.btn_save)
        button_layout.addWidget(self.btn_cancel)

        layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 连接信号
        self.connect_signals()

    def create_kuaidaili_widget(self):
        """创建快代理API配置界面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 快代理API配置组
        api_group = QGroupBox("快代理API配置")
        api_layout = QFormLayout(api_group)

        # Secret ID
        self.le_secret_id = QLineEdit()
        self.le_secret_id.setPlaceholderText("请输入快代理的secret_id")
        api_layout.addRow("Secret ID:", self.le_secret_id)

        # Signature
        self.le_signature = QLineEdit()
        self.le_signature.setPlaceholderText("请输入快代理的signature")
        api_layout.addRow("Signature:", self.le_signature)

        # 代理数量
        self.sb_proxy_count = QSpinBox()
        self.sb_proxy_count.setRange(1, 100)
        self.sb_proxy_count.setValue(10)
        api_layout.addRow("每次获取代理数量:", self.sb_proxy_count)

        layout.addWidget(api_group)

        # 快代理操作按钮
        kuaidaili_button_layout = QHBoxLayout()
        self.btn_test_api = QPushButton("测试API连接")
        self.btn_fetch_proxies = QPushButton("获取代理")
        self.btn_clear_cache = QPushButton("清空显示")

        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """
        
        self.btn_test_api.setStyleSheet(button_style)
        self.btn_fetch_proxies.setStyleSheet(button_style)
        self.btn_clear_cache.setStyleSheet(button_style.replace("#007bff", "#ffc107").replace("#0056b3", "#e0a800"))

        kuaidaili_button_layout.addWidget(self.btn_test_api)
        kuaidaili_button_layout.addWidget(self.btn_fetch_proxies)
        kuaidaili_button_layout.addWidget(self.btn_clear_cache)
        kuaidaili_button_layout.addStretch()

        layout.addLayout(kuaidaili_button_layout)

        # 快代理状态信息
        status_group = QGroupBox("快代理状态")
        status_layout = QVBoxLayout(status_group)

        self.lbl_api_status = QLabel("API状态: 未测试")
        self.lbl_cache_info = QLabel("获取状态: 无")
        
        status_layout.addWidget(self.lbl_api_status)
        status_layout.addWidget(self.lbl_cache_info)

        layout.addWidget(status_group)

        # 获取的代理列表
        proxy_group = QGroupBox("获取的代理列表")
        proxy_layout = QVBoxLayout(proxy_group)

        self.te_fetched_proxies = QTextEdit()
        self.te_fetched_proxies.setReadOnly(True)
        self.te_fetched_proxies.setFont(QFont("Consolas", 10))
        self.te_fetched_proxies.setMaximumHeight(150)
        self.te_fetched_proxies.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """)
        proxy_layout.addWidget(self.te_fetched_proxies)

        layout.addWidget(proxy_group)

        layout.addStretch()
        return widget

    def connect_signals(self):
        """连接信号槽"""
        # 快代理相关
        self.btn_test_api.clicked.connect(self.test_kuaidaili_api)
        self.btn_fetch_proxies.clicked.connect(self.fetch_kuaidaili_proxies)
        self.btn_clear_cache.clicked.connect(self.clear_display)
        
        # 通用按钮
        self.btn_save.clicked.connect(self.save_config)
        self.btn_cancel.clicked.connect(self.reject)

    def load_config(self):
        """加载配置"""
        try:
            if self.config.is_valid():
                self.cb_enable_proxy.setChecked(True)
                self.le_secret_id.setText(self.config.secret_id)
                self.le_signature.setText(self.config.signature)
                self.lbl_api_status.setText("API状态: ✅ 已配置")
            else:
                self.cb_enable_proxy.setChecked(False)
                self.lbl_api_status.setText("API状态: ❌ 未配置")
        except ConfigException:
            self.cb_enable_proxy.setChecked(False)
            self.lbl_api_status.setText("API状态: ❌ 未配置")

    def save_config(self):
        """保存配置"""
        enabled = self.cb_enable_proxy.isChecked()
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()

        if enabled and (not secret_id or not signature):
            QMessageBox.warning(
                self, "配置不完整",
                "❌ 错误：启用代理但未配置快代理API！\n\n"
                "请填写快代理的Secret ID和Signature。"
            )
            return

        try:
            # 更新配置
            if enabled and secret_id and signature:
                self.config = KuaidailiConfig(secret_id=secret_id, signature=signature)
                QMessageBox.information(
                    self, "保存成功",
                    f"✅ 快代理配置已保存！\n\n"
                    f"代理启用状态: 已启用\n"
                    f"快代理API: 已配置\n"
                    f"每次获取代理数量: {self.sb_proxy_count.value()}"
                )
            else:
                self.config = KuaidailiConfig()
                QMessageBox.information(
                    self, "保存成功",
                    "代理配置已保存！\n\n"
                    "代理启用状态: 已禁用"
                )

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置时发生错误：{str(e)}")

    def test_kuaidaili_api(self):
        """测试快代理API连接"""
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()

        if not secret_id or not signature:
            QMessageBox.warning(self, "输入错误", "请先输入Secret ID和Signature！")
            return

        try:
            # 创建临时客户端进行测试
            temp_config = KuaidailiConfig(secret_id=secret_id, signature=signature)
            temp_client = KuaidailiClient(config=temp_config)

            # 测试连接
            success, message = temp_client.test_connection()

            if success:
                self.lbl_api_status.setText("API状态: ✅ 连接成功")
                QMessageBox.information(self, "测试成功", f"快代理API连接成功！\n{message}")
            else:
                self.lbl_api_status.setText("API状态: ❌ 连接失败")
                QMessageBox.warning(self, "测试失败", f"快代理API连接失败！\n{message}")

        except Exception as e:
            self.lbl_api_status.setText("API状态: ❌ 测试异常")
            QMessageBox.critical(self, "测试异常", f"测试过程中发生错误：{str(e)}")

    def fetch_kuaidaili_proxies(self):
        """获取快代理"""
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()

        if not secret_id or not signature:
            QMessageBox.warning(self, "输入错误", "请先输入Secret ID和Signature！")
            return

        try:
            # 创建临时客户端
            temp_config = KuaidailiConfig(secret_id=secret_id, signature=signature)
            temp_client = KuaidailiClient(config=temp_config)

            proxy_count = self.sb_proxy_count.value()

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            # 获取代理
            proxies = temp_client.get_proxy_list(num=proxy_count, validate=False)

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            if proxies:
                proxy_text = '\n'.join(proxies)
                self.te_fetched_proxies.setPlainText(proxy_text)
                self.lbl_cache_info.setText(f"获取状态: ✅ 成功获取 {len(proxies)} 个代理")
                QMessageBox.information(self, "获取成功", f"成功获取 {len(proxies)} 个代理！")
            else:
                self.lbl_cache_info.setText("获取状态: ❌ 获取失败")
                QMessageBox.warning(self, "获取失败", "无法获取代理，请检查API配置和网络连接！")

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.lbl_cache_info.setText("获取状态: ❌ 获取异常")
            QMessageBox.critical(self, "获取异常", f"获取代理时发生错误：{str(e)}")

    def clear_display(self):
        """清空显示"""
        self.te_fetched_proxies.clear()
        self.lbl_cache_info.setText("获取状态: 已清空")
        QMessageBox.information(self, "清空成功", "代理显示已清空！")

    def get_config(self):
        """获取当前配置"""
        return self.config
