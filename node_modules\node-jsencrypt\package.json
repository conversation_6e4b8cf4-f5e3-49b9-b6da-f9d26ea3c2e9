{"name": "node-jsencrypt", "version": "1.0.0", "description": "A port of JSEncrypt to NodeJS.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/ArnaudValensi/node-jsencrypt.git"}, "keywords": ["jsencrypt", "rsa", "crypto"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ArnaudValensi/node-jsencrypt/issues"}, "homepage": "https://github.com/ArnaudValensi/node-jsencrypt#readme", "dependencies": {"get-random-values": "^1.2.0"}}