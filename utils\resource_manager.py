"""
资源管理模块
"""
import os
import sys
import tempfile
import shutil
import zipfile
import json
from pathlib import Path


class ResourceManager:
    """资源管理类"""
    
    def __init__(self):
        self.runtime_path = None
        self.node_path = None
        self.js_path = None
        self.temp_dir = None
        self.initialized = False
        
    def initialize_runtime(self):
        """初始化运行时环境"""
        try:
            # 设置运行时路径
            self._setup_runtime_path()
            
            # 检查运行时完整性
            if not self._check_runtime_integrity():
                self._extract_resources()
                
            # 设置路径
            self._setup_paths()
            
            # 创建临时目录
            self._setup_temp_dir()
            
            self.initialized = True
            return True
            
        except Exception as e:
            print(f"初始化运行时环境失败: {e}")
            return False
            
    def _setup_runtime_path(self):
        """设置运行时目录路径"""
        # 优先使用ProgramData目录
        program_data = os.environ.get('PROGRAMDATA', 'C:\\ProgramData')
        runtime_base = os.path.join(program_data, 'SocialOrgMonitor', 'runtime')
        
        # 检查是否有写入权限
        try:
            os.makedirs(runtime_base, exist_ok=True)
            # 测试写入权限
            test_file = os.path.join(runtime_base, 'test_write.tmp')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            self.runtime_path = runtime_base
        except (OSError, PermissionError):
            # 使用用户临时目录作为备选
            user_temp = tempfile.gettempdir()
            runtime_base = os.path.join(user_temp, f'SocialOrgMonitor_{os.getlogin()}', 'runtime')
            os.makedirs(runtime_base, exist_ok=True)
            self.runtime_path = runtime_base
            
    def _check_runtime_integrity(self):
        """检查运行时完整性"""
        if not self.runtime_path or not os.path.exists(self.runtime_path):
            return False

        # 检查必要文件是否存在
        required_files = [
            'nodejs/node.exe',
            'js_modules/main.js',
            'js_modules/node_modules/node-jsencrypt/index.js'
        ]

        for file_path in required_files:
            full_path = os.path.join(self.runtime_path, file_path)
            if not os.path.exists(full_path):
                print(f"缺少必要文件: {full_path}")
                return False

        # 检查文件是否可执行
        node_exe = os.path.join(self.runtime_path, 'nodejs/node.exe')
        if not os.access(node_exe, os.X_OK):
            print(f"Node.js可执行文件无执行权限: {node_exe}")
            return False

        return True
        
    def _extract_resources(self):
        """解压资源文件"""
        print("正在解压运行时资源...")
        
        # 获取资源文件路径
        if getattr(sys, 'frozen', False):
            # 打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            
        resources_path = os.path.join(base_path, 'resources')
        
        # 复制Node.js运行时
        self._copy_nodejs_runtime(resources_path)
        
        # 复制JavaScript文件
        self._copy_js_modules(resources_path)
        
        print("运行时资源解压完成")
        
    def _copy_nodejs_runtime(self, resources_path):
        """复制Node.js运行时"""
        nodejs_src = os.path.join(resources_path, 'nodejs')
        nodejs_dst = os.path.join(self.runtime_path, 'nodejs')
        
        if os.path.exists(nodejs_src):
            if os.path.exists(nodejs_dst):
                shutil.rmtree(nodejs_dst)
            shutil.copytree(nodejs_src, nodejs_dst)
        else:
            # 如果没有打包Node.js，尝试使用系统Node.js
            print("警告: 未找到打包的Node.js运行时，将尝试使用系统Node.js")
            
    def _copy_js_modules(self, resources_path):
        """复制JavaScript模块"""
        # 复制main.js
        main_js_src = os.path.join(os.path.dirname(resources_path), 'main.js')
        js_modules_dst = os.path.join(self.runtime_path, 'js_modules')
        os.makedirs(js_modules_dst, exist_ok=True)
        
        if os.path.exists(main_js_src):
            shutil.copy2(main_js_src, os.path.join(js_modules_dst, 'main.js'))
            
        # 复制node_modules
        node_modules_src = os.path.join(os.path.dirname(resources_path), 'node_modules')
        node_modules_dst = os.path.join(js_modules_dst, 'node_modules')
        
        if os.path.exists(node_modules_src):
            if os.path.exists(node_modules_dst):
                shutil.rmtree(node_modules_dst)
            shutil.copytree(node_modules_src, node_modules_dst)
            
    def _setup_paths(self):
        """设置各种路径"""
        if not self.runtime_path:
            return
            
        # Node.js路径
        self.node_path = os.path.join(self.runtime_path, 'nodejs', 'node.exe')
        if not os.path.exists(self.node_path):
            # 尝试使用系统Node.js
            self.node_path = shutil.which('node')
            
        # JavaScript文件路径 - 尝试多个可能的位置
        possible_js_paths = [
            os.path.join(self.runtime_path, 'js_modules', 'main.js'),  # 首选位置
            os.path.join(self.runtime_path, 'main.js'),  # 备选位置1
            'main.js',  # 备选位置2：当前目录
        ]

        self.js_path = None
        for js_path in possible_js_paths:
            if os.path.exists(js_path):
                self.js_path = js_path
                print(f"✅ 找到JavaScript文件: {js_path}")
                break

        if not self.js_path:
            print("⚠️ 未找到JavaScript文件，将使用当前目录下的main.js作为备选")
            self.js_path = 'main.js'
        
    def _setup_temp_dir(self):
        """设置临时目录"""
        self.temp_dir = os.path.join(self.runtime_path, 'temp')
        os.makedirs(self.temp_dir, exist_ok=True)
        
    def get_node_path(self):
        """获取Node.js路径"""
        return self.node_path
        
    def get_js_file_path(self):
        """获取JavaScript文件路径"""
        return self.js_path
        
    def get_temp_dir(self):
        """获取临时目录"""
        return self.temp_dir
        
    def cleanup(self):
        """清理临时文件"""
        try:
            if self.temp_dir and os.path.exists(self.temp_dir):
                # 清理临时目录中的文件
                for file in os.listdir(self.temp_dir):
                    file_path = os.path.join(self.temp_dir, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
        except Exception as e:
            print(f"清理临时文件时出错: {e}")
            
    def is_initialized(self):
        """检查是否已初始化"""
        return self.initialized
