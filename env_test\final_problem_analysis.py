"""
最终问题分析：确定"常州市长荡湖高级中学"重复通知的根本原因
"""
import sys
import os
import sqlite3
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.database import DatabaseManager

class FinalProblemAnalysis:
    """最终问题分析器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.target_school = "常州市长荡湖高级中学"
        self.target_keyword = "高级中学"
        
    def analyze_database_content(self):
        """深入分析数据库内容"""
        print("=" * 60)
        print("🔍 深入分析数据库内容")
        print("=" * 60)
        
        # 直接查询数据库
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        
        # 查询所有高级中学记录
        cursor.execute("SELECT aaae0102, aaae0103, aaae0113, aaae0123 FROM gaoji_zhongxue")
        all_records = cursor.fetchall()
        
        print(f"📊 数据库中共有 {len(all_records)} 条高级中学记录")
        
        # 查找包含"长荡湖"的记录
        changdanghu_records = []
        for record in all_records:
            if "长荡湖" in record[1]:  # aaae0103是学校名称
                changdanghu_records.append(record)
        
        print(f"🎯 包含'长荡湖'的记录: {len(changdanghu_records)} 条")
        
        if changdanghu_records:
            print("📋 详细记录:")
            for i, record in enumerate(changdanghu_records):
                print(f"   {i+1}. 代码: {record[0]}")
                print(f"      名称: '{record[1]}'")
                print(f"      区域: {record[2]}")
                print(f"      类型: {record[3]}")
                print()
        
        # 查找包含"常州"的记录
        changzhou_records = []
        for record in all_records:
            if "常州" in record[1]:
                changzhou_records.append(record)
        
        print(f"🏙️ 包含'常州'的记录: {len(changzhou_records)} 条")
        
        if changzhou_records:
            print("📋 常州相关记录:")
            for i, record in enumerate(changzhou_records[:10]):  # 只显示前10条
                print(f"   {i+1}. {record[0]} - '{record[1]}'")
            if len(changzhou_records) > 10:
                print(f"   ... 还有 {len(changzhou_records) - 10} 条记录")
        
        # 精确查找目标学校
        exact_matches = []
        for record in all_records:
            if record[1].strip() == self.target_school:
                exact_matches.append(record)
        
        print(f"\n🎯 精确匹配'{self.target_school}': {len(exact_matches)} 条")
        
        # 模糊匹配分析
        fuzzy_matches = []
        target_keywords = ["常州", "长荡湖", "高级中学"]
        
        for record in all_records:
            name = record[1].strip()
            match_count = sum(1 for keyword in target_keywords if keyword in name)
            if match_count >= 2:  # 至少匹配2个关键词
                fuzzy_matches.append((record, match_count))
        
        print(f"\n🔍 模糊匹配分析 (至少包含2个关键词):")
        print(f"   匹配记录: {len(fuzzy_matches)} 条")
        
        if fuzzy_matches:
            print("📋 模糊匹配记录:")
            for i, (record, match_count) in enumerate(fuzzy_matches):
                print(f"   {i+1}. {record[0]} - '{record[1]}' (匹配度: {match_count}/3)")
        
        conn.close()
        
        return {
            'total_records': len(all_records),
            'changdanghu_records': changdanghu_records,
            'changzhou_records': changzhou_records,
            'exact_matches': exact_matches,
            'fuzzy_matches': fuzzy_matches
        }
    
    def analyze_name_variations(self):
        """分析名称变体"""
        print("=" * 60)
        print("📝 分析可能的名称变体")
        print("=" * 60)
        
        # 可能的名称变体
        possible_variations = [
            "常州市长荡湖高级中学",
            "常州长荡湖高级中学",
            "长荡湖高级中学",
            "常州市长荡湖中学",
            "江苏省常州市长荡湖高级中学",
            "常州市金坛区长荡湖高级中学",
            "金坛区长荡湖高级中学"
        ]
        
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        
        print("🔍 检查可能的名称变体:")
        found_variations = []
        
        for variation in possible_variations:
            cursor.execute("SELECT aaae0102, aaae0103 FROM gaoji_zhongxue WHERE aaae0103 LIKE ?", (f'%{variation}%',))
            matches = cursor.fetchall()
            
            if matches:
                found_variations.append((variation, matches))
                print(f"   ✅ '{variation}': {len(matches)} 条匹配")
                for match in matches:
                    print(f"      {match[0]} - '{match[1]}'")
            else:
                print(f"   ❌ '{variation}': 无匹配")
        
        conn.close()
        
        return found_variations
    
    def check_data_integrity_issues(self):
        """检查数据完整性问题"""
        print("=" * 60)
        print("🔧 检查数据完整性问题")
        print("=" * 60)
        
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        
        # 检查空值
        cursor.execute("SELECT COUNT(*) FROM gaoji_zhongxue WHERE aaae0102 IS NULL OR aaae0102 = ''")
        empty_codes = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM gaoji_zhongxue WHERE aaae0103 IS NULL OR aaae0103 = ''")
        empty_names = cursor.fetchone()[0]
        
        print(f"📊 数据完整性检查:")
        print(f"   空组织代码: {empty_codes} 条")
        print(f"   空组织名称: {empty_names} 条")
        
        # 检查重复代码
        cursor.execute("""
            SELECT aaae0102, COUNT(*) as count 
            FROM gaoji_zhongxue 
            GROUP BY aaae0102 
            HAVING COUNT(*) > 1
        """)
        duplicate_codes = cursor.fetchall()
        
        print(f"   重复组织代码: {len(duplicate_codes)} 个")
        
        if duplicate_codes:
            print("   重复代码详情:")
            for code, count in duplicate_codes:
                print(f"      {code}: {count} 次")
        
        # 检查名称格式问题
        cursor.execute("SELECT aaae0102, aaae0103 FROM gaoji_zhongxue")
        all_records = cursor.fetchall()
        
        format_issues = []
        for code, name in all_records:
            if name != name.strip():
                format_issues.append((code, f"名称有空格: '{name}'"))
            if code != code.strip():
                format_issues.append((code, f"代码有空格: '{code}'"))
        
        print(f"   格式问题: {len(format_issues)} 条")
        
        if format_issues and len(format_issues) <= 10:
            print("   格式问题详情:")
            for code, issue in format_issues[:10]:
                print(f"      {code}: {issue}")
        
        conn.close()
        
        return {
            'empty_codes': empty_codes,
            'empty_names': empty_names,
            'duplicate_codes': duplicate_codes,
            'format_issues': format_issues
        }
    
    def generate_problem_report(self):
        """生成问题报告"""
        print("=" * 60)
        print("📋 问题根因分析报告")
        print("=" * 60)
        
        # 分析数据库内容
        db_analysis = self.analyze_database_content()
        
        # 分析名称变体
        name_variations = self.analyze_name_variations()
        
        # 检查数据完整性
        integrity_check = self.check_data_integrity_issues()
        
        print("=" * 60)
        print("🎯 问题根因总结")
        print("=" * 60)
        
        print("📊 关键发现:")
        print(f"1. 数据库中总记录数: {db_analysis['total_records']}")
        print(f"2. 精确匹配目标学校: {len(db_analysis['exact_matches'])} 条")
        print(f"3. 包含'长荡湖'的记录: {len(db_analysis['changdanghu_records'])} 条")
        print(f"4. 包含'常州'的记录: {len(db_analysis['changzhou_records'])} 条")
        print(f"5. 模糊匹配记录: {len(db_analysis['fuzzy_matches'])} 条")
        print(f"6. 找到的名称变体: {len(name_variations)} 种")
        
        print(f"\n🔍 重复通知的根本原因:")
        
        if len(db_analysis['exact_matches']) == 0:
            print("✅ 确认：目标学校在数据库中不存在")
            print("   这意味着每次监控时，如果网站数据中包含该学校，")
            print("   都会被识别为'新增数据'，从而触发通知。")
            
            if len(db_analysis['fuzzy_matches']) > 0:
                print("\n⚠️ 但是发现了相似的记录:")
                for record, match_count in db_analysis['fuzzy_matches']:
                    print(f"   - {record[0]}: '{record[1]}' (相似度: {match_count}/3)")
                print("   可能的原因:")
                print("   1. 学校名称在网站上发生了变化")
                print("   2. 数据获取时的名称格式不一致")
                print("   3. 学校可能有多个官方名称")
        
        print(f"\n💡 解决方案建议:")
        print("1. 【立即解决】手动将目标学校添加到数据库中")
        print("2. 【长期方案】实现智能名称匹配机制")
        print("3. 【监控优化】添加通知去重机制")
        print("4. 【数据质量】定期检查和清理数据格式问题")
        
        if len(name_variations) > 0:
            print(f"\n📝 建议检查的名称变体:")
            for variation, matches in name_variations:
                print(f"   - '{variation}': {len(matches)} 条匹配")
        
        return {
            'db_analysis': db_analysis,
            'name_variations': name_variations,
            'integrity_check': integrity_check
        }

if __name__ == "__main__":
    print("🚀 开始最终问题分析")
    print("目标: 确定'常州市长荡湖高级中学'重复通知的根本原因")
    print("时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print()
    
    analyzer = FinalProblemAnalysis()
    report = analyzer.generate_problem_report()
    
    print("\n" + "=" * 60)
    print("✅ 分析完成！")
    print("=" * 60)
