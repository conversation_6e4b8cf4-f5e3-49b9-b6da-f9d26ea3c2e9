"""
安全配置管理模块 - 可选的高级保护方案
"""
import base64
import hashlib
from cryptography.fernet import Fernet


class SecureConfigManager:
    """安全配置管理器 - 用于保护敏感配置"""
    
    def __init__(self):
        # 使用固定的密钥（在实际部署中应该更安全地生成）
        self.key = self._generate_key()
        self.cipher = Fernet(self.key)
        
    def _generate_key(self):
        """生成加密密钥"""
        # 使用程序特定的字符串生成固定密钥
        seed = "中国社会组织监控工具_安全配置_2024"
        key_bytes = hashlib.sha256(seed.encode()).digest()
        return base64.urlsafe_b64encode(key_bytes)
    
    def encrypt_value(self, value):
        """加密配置值"""
        try:
            encrypted = self.cipher.encrypt(str(value).encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            print(f"加密失败: {e}")
            return str(value)  # 失败时返回原值
    
    def decrypt_value(self, encrypted_value):
        """解密配置值"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_value.encode())
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            print(f"解密失败: {e}")
            return encrypted_value  # 失败时返回原值
    
    def get_secure_interval(self):
        """获取安全的监控间隔"""
        # 这些值被"隐藏"在代码中，客户难以修改
        encrypted_min = "gAAAAABnEQR5X1Y2Z3..."  # 实际应该是加密后的值
        encrypted_max = "gAAAAABnEQR5Y2Z4X1..."  # 实际应该是加密后的值
        
        # 直接返回硬编码值，更简单可靠
        return 5.0, 6.0


# 使用示例（可选）
def get_protected_interval():
    """获取受保护的监控间隔"""
    # 方案1：直接硬编码（推荐）
    return 5.0, 6.0
    
    # 方案2：使用加密（如果需要更高安全性）
    # secure_config = SecureConfigManager()
    # return secure_config.get_secure_interval()
