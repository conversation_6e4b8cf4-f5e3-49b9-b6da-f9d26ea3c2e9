const JSEncrypt = require('node-jsencrypt');
var u = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
function get_RSAcode(t) {
    var e = new JSEncrypt;
    e.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCCsYUGHMhjSzdMqn9JzPfKs9JbxXTPtHofTv7reV0HrEz4brnE6ZJpNn5s934KO3L4QDF7ELHysIiounhhpF1bewW9jKdcpZA5M1CkGHKcwpLA2liaqOlt/0Mf3ui9jxR9AHxUMFVGfJ6Q4+cEmDBUAEOXlxqk4ZjGpubwGNk9XQIDAQAB");

    result = e.encrypt(t.toString())
    return result
}
var encodedata = function (t) {
    var a, i, e, s, n, l = "", c = "", o = "", r = 0;
    do {
        e = (a = t.charCodeAt(r++)) >> 2,
            s = (3 & a) << 4 | (i = t.charCodeAt(r++)) >> 4,
            n = (15 & i) << 2 | (c = t.charCodeAt(r++)) >> 6,
            o = 63 & c,
            isNaN(i) ? n = o = 64 : isNaN(c) && (o = 64),
            l = l + u.charAt(e) + u.charAt(s) + u.charAt(n) + u.charAt(o),
            a = i = c = "",
            e = s = n = o = ""
    } while (r < t.length);
    return l
};
function get_params(avalue, bvalue, moveX) {
    a = get_RSAcode(parseInt(moveX) + "");
    i = {
        a: encodedata(avalue),
        b: encodedata(bvalue),
        c: encodedata(a)
    };
    return i;
};