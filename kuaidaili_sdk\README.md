# 快代理SDK

一个轻量级的快代理API客户端，提供完整的代理获取、验证和池管理功能。

## 特性

- 🚀 **简单易用**: 提供简洁的API接口
- 🔍 **代理验证**: 内置代理可用性验证
- 🏊 **代理池管理**: 智能代理池，自动轮换和刷新
- ⚙️ **灵活配置**: 支持多种配置方式
- 🛡️ **错误处理**: 完善的异常处理机制
- 📊 **状态监控**: 代理池状态实时监控

## 安装

```bash
# 将kuaidaili_sdk文件夹复制到你的项目中
# 或者添加到Python路径
```

## 快速开始

### 基本使用

```python
from kuaidaili_sdk import KuaidailiClient

# 创建客户端
client = KuaidailiClient(
    secret_id="your_secret_id",
    signature="your_signature"
)

# 获取单个代理
proxy = client.get_proxy()
print(f"获取到代理: {proxy}")

# 获取验证过的代理
verified_proxy = client.get_verified_proxy()
print(f"验证通过的代理: {verified_proxy}")

# 获取代理列表
proxy_list = client.get_proxy_list(num=10, validate=True)
print(f"获取到 {len(proxy_list)} 个可用代理")
```

### 使用配置文件

```python
from kuaidaili_sdk import KuaidailiClient

# 从配置文件创建客户端
client = KuaidailiClient.from_config_file("config.ini")

# 配置文件格式 (config.ini):
# [Kuaidaili]
# secret_id = your_secret_id
# signature = your_signature
```

### 使用环境变量

```python
import os
from kuaidaili_sdk import KuaidailiClient

# 设置环境变量
os.environ['KUAIDAILI_SECRET_ID'] = 'your_secret_id'
os.environ['KUAIDAILI_SIGNATURE'] = 'your_signature'

# 从环境变量创建客户端
client = KuaidailiClient.from_env()
```

### 代理池管理

```python
from kuaidaili_sdk import KuaidailiClient, ProxyPool

# 创建客户端
client = KuaidailiClient(
    secret_id="your_secret_id",
    signature="your_signature"
)

# 创建代理池
proxy_pool = ProxyPool(
    client=client,
    pool_size=20,        # 代理池大小
    auto_refresh=True,   # 自动刷新
    refresh_interval=300 # 刷新间隔(秒)
)

# 从代理池获取代理
proxy = proxy_pool.get_proxy()
print(f"从代理池获取: {proxy}")

# 查看代理池状态
status = proxy_pool.get_pool_status()
print(f"代理池状态: {status}")
```

### 使用代理会话

```python
from kuaidaili_sdk import KuaidailiClient, ProxyPool

client = KuaidailiClient(secret_id="xxx", signature="xxx")
proxy_pool = ProxyPool(client)

# 使用代理会话
with proxy_pool.get_session() as session:
    response = session.get("http://httpbin.org/ip")
    print(response.json())
```

## API 参考

### KuaidailiClient

主要的API客户端类。

#### 方法

- `get_proxy(validate=True)`: 获取单个代理
- `get_verified_proxy(max_attempts=5)`: 获取验证过的代理
- `get_proxy_list(num=10, validate=False)`: 获取代理列表
- `get_cached_proxies(num=10, force_refresh=False)`: 获取缓存代理
- `validate_proxy(proxy)`: 验证代理可用性
- `test_connection()`: 测试API连接

### ProxyPool

代理池管理类。

#### 方法

- `get_proxy(validate=True)`: 从池中获取代理
- `refresh_pool(force=False)`: 刷新代理池
- `mark_proxy_failed(proxy)`: 标记代理失败
- `get_pool_status()`: 获取池状态
- `get_session()`: 获取代理会话

### ProxyValidator

代理验证器类。

#### 方法

- `validate_proxy(proxy, test_url=None)`: 验证单个代理
- `validate_proxy_list(proxies, max_workers=5)`: 批量验证
- `get_valid_proxies(proxies)`: 筛选可用代理

## 配置选项

### KuaidailiConfig

```python
from kuaidaili_sdk import KuaidailiConfig

# 直接创建
config = KuaidailiConfig(
    secret_id="your_secret_id",
    signature="your_signature"
)

# 从文件加载
config = KuaidailiConfig()
config.load_from_file("config.ini")

# 从环境变量
config = KuaidailiConfig.from_env()

# 从字典
config = KuaidailiConfig.from_dict({
    'secret_id': 'xxx',
    'signature': 'xxx'
})
```

## 异常处理

```python
from kuaidaili_sdk import KuaidailiClient
from kuaidaili_sdk.exceptions import APIException, ProxyException

try:
    client = KuaidailiClient(secret_id="xxx", signature="xxx")
    proxy = client.get_proxy()
except APIException as e:
    print(f"API异常: {e}")
except ProxyException as e:
    print(f"代理异常: {e}")
```

## 最佳实践

1. **使用代理池**: 对于频繁请求，建议使用代理池管理
2. **启用验证**: 在生产环境中建议启用代理验证
3. **合理设置超时**: 根据网络环境调整超时时间
4. **监控状态**: 定期检查代理池状态
5. **异常处理**: 做好异常处理和重试机制

## 注意事项

- 需要有效的快代理API凭证
- 代理验证会消耗额外时间
- 建议在稳定网络环境下使用
- 注意API调用频率限制

## 许可证

本SDK提取自中国社会组织监控工具项目，仅供学习和研究使用。
