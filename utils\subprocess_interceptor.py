"""
全局subprocess拦截器
确保所有子进程都隐藏窗口，彻底解决CMD黑框弹出问题
"""

import os
import subprocess
import sys

# 保存原始的subprocess.Popen
_original_popen = subprocess.Popen

def _hidden_popen(*args, **kwargs):
    """全局subprocess.Popen拦截器，确保所有子进程都隐藏窗口"""
    if os.name == 'nt':  # Windows
        # 确保startupinfo设置
        if 'startupinfo' not in kwargs:
            startupinfo = subprocess.STARTUPINFO()
            kwargs['startupinfo'] = startupinfo
        else:
            startupinfo = kwargs['startupinfo']
        
        # 设置隐藏窗口标志
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
        
        # 确保creationflags设置
        if 'creationflags' not in kwargs:
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
        else:
            kwargs['creationflags'] |= subprocess.CREATE_NO_WINDOW
    
    return _original_popen(*args, **kwargs)

def apply_global_interceptor():
    """应用全局subprocess拦截器"""
    subprocess.Popen = _hidden_popen
    print("✅ 全局subprocess拦截器已应用")

def restore_original_popen():
    """恢复原始的subprocess.Popen"""
    subprocess.Popen = _original_popen
    print("✅ 已恢复原始subprocess.Popen")

# 立即应用拦截器
apply_global_interceptor()

# 同时拦截execjs可能使用的其他方式
try:
    import execjs._external_runtime
    
    # 保存原始的_exec_with_pipe方法
    if hasattr(execjs._external_runtime.ExternalRuntime, '_exec_with_pipe'):
        _original_exec_with_pipe = execjs._external_runtime.ExternalRuntime._exec_with_pipe
        
        def _hidden_exec_with_pipe(self, source, *args, **kwargs):
            """拦截execjs的_exec_with_pipe方法"""
            # 临时替换subprocess.Popen
            original_popen = subprocess.Popen
            subprocess.Popen = _hidden_popen
            
            try:
                return _original_exec_with_pipe(self, source, *args, **kwargs)
            finally:
                subprocess.Popen = original_popen
        
        execjs._external_runtime.ExternalRuntime._exec_with_pipe = _hidden_exec_with_pipe
        print("✅ execjs拦截器已应用")
        
except ImportError:
    pass

# 拦截可能的其他进程创建方式
try:
    import os
    _original_system = os.system
    
    def _hidden_system(command):
        """拦截os.system调用"""
        if os.name == 'nt':
            # 在Windows上，尝试使用subprocess替代os.system
            try:
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    startupinfo=subprocess.STARTUPINFO(),
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                return result.returncode
            except:
                return _original_system(command)
        else:
            return _original_system(command)
    
    os.system = _hidden_system
    print("✅ os.system拦截器已应用")
    
except:
    pass
