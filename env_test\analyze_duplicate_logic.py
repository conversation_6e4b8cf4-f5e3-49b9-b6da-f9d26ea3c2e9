"""
专门分析重复通知逻辑问题的测试程序
不依赖网络连接，通过模拟数据来分析问题根源
"""
import sys
import os
import sqlite3
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.database import DatabaseManager
from core.monitor import MonitorCore
from utils.config import ConfigManager

class DuplicateLogicAnalyzer:
    """重复通知逻辑分析器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.config_manager = ConfigManager()
        self.target_school = "常州市长荡湖高级中学"
        self.target_keyword = "高级中学"
        
    def check_current_database_state(self):
        """检查当前数据库状态"""
        print("=" * 60)
        print("🔍 检查当前数据库状态")
        print("=" * 60)
        
        # 获取所有数据
        all_data = self.db_manager.get_all_data(self.target_keyword)
        existing_codes = self.db_manager.get_existing_codes(self.target_keyword)
        
        print(f"📊 数据库状态:")
        print(f"   总记录数: {len(all_data)}")
        print(f"   唯一代码数: {len(existing_codes)}")
        
        # 查找目标学校
        target_records = []
        for record in all_data:
            if self.target_school in record.get('aaae0103', ''):
                target_records.append(record)
        
        print(f"🎯 目标学校记录: {len(target_records)} 条")
        
        if target_records:
            for i, record in enumerate(target_records):
                print(f"   记录 {i+1}: {record.get('aaae0102', 'N/A')} - {record.get('aaae0103', 'N/A')}")
        
        return all_data, existing_codes, target_records
    
    def simulate_data_scenarios(self):
        """模拟各种数据场景"""
        print("=" * 60)
        print("🧪 模拟各种数据场景")
        print("=" * 60)
        
        # 获取当前数据库状态
        db_data = self.db_manager.get_all_data(self.target_keyword)
        existing_codes = self.db_manager.get_existing_codes(self.target_keyword)
        
        # 创建监控核心实例
        monitor = MonitorCore()
        
        # 场景1: 目标学校已存在于数据库中
        print("\n--- 场景1: 目标学校已存在于数据库中 ---")
        
        # 假设目标学校有一个固定的组织代码
        target_code = "TEST_CHANGZHOU_001"
        
        # 模拟网站数据包含目标学校
        scenario1_data = [
            {
                'aaae0102': target_code,
                'aaae0103': self.target_school,
                'aaae0113': 'test_area',
                'aaae0123': 'test_type'
            }
        ]
        
        # 先将目标学校添加到数据库（模拟已存在的情况）
        print(f"📥 模拟数据库中已有目标学校记录")
        insert_count = self.db_manager.insert_data(self.target_keyword, scenario1_data)
        print(f"   插入结果: {insert_count} 条")
        
        # 现在模拟同步过程
        sync_result1 = monitor._sync_database_with_website(self.target_keyword, scenario1_data)
        print(f"🔄 同步结果:")
        print(f"   新增: {len(sync_result1['new_data'])} 条")
        print(f"   删除: {len(sync_result1['deleted_data'])} 条")
        
        # 检查目标学校是否被识别为新增
        target_in_new1 = any(self.target_school in item.get('aaae0103', '') 
                            for item in sync_result1['new_data'])
        print(f"   目标学校被识别为新增: {target_in_new1}")
        
        # 场景2: 目标学校不在数据库中，但在网站数据中
        print("\n--- 场景2: 目标学校不在数据库中 ---")
        
        # 先删除目标学校记录
        self.db_manager.delete_data(self.target_keyword, scenario1_data)
        print(f"🗑️ 已从数据库删除目标学校记录")
        
        # 再次同步
        sync_result2 = monitor._sync_database_with_website(self.target_keyword, scenario1_data)
        print(f"🔄 同步结果:")
        print(f"   新增: {len(sync_result2['new_data'])} 条")
        print(f"   删除: {len(sync_result2['deleted_data'])} 条")
        
        target_in_new2 = any(self.target_school in item.get('aaae0103', '') 
                            for item in sync_result2['new_data'])
        print(f"   目标学校被识别为新增: {target_in_new2}")
        
        # 场景3: 数据库中有目标学校，但网站数据中没有
        print("\n--- 场景3: 数据库中有目标学校，但网站数据中没有 ---")
        
        # 重新添加到数据库
        self.db_manager.insert_data(self.target_keyword, scenario1_data)
        
        # 模拟网站数据不包含目标学校
        scenario3_data = [
            {
                'aaae0102': 'OTHER_SCHOOL_001',
                'aaae0103': '其他学校1',
                'aaae0113': 'other_area',
                'aaae0123': 'other_type'
            }
        ]
        
        sync_result3 = monitor._sync_database_with_website(self.target_keyword, scenario3_data)
        print(f"🔄 同步结果:")
        print(f"   新增: {len(sync_result3['new_data'])} 条")
        print(f"   删除: {len(sync_result3['deleted_data'])} 条")
        
        target_in_deleted3 = any(self.target_school in item.get('aaae0103', '') 
                                for item in sync_result3['deleted_data'])
        print(f"   目标学校被识别为删除: {target_in_deleted3}")
        
        # 清理测试数据
        self.db_manager.delete_data(self.target_keyword, scenario1_data)
        self.db_manager.delete_data(self.target_keyword, scenario3_data)
        
        return {
            'scenario1': {'target_in_new': target_in_new1, 'sync_result': sync_result1},
            'scenario2': {'target_in_new': target_in_new2, 'sync_result': sync_result2},
            'scenario3': {'target_in_deleted': target_in_deleted3, 'sync_result': sync_result3}
        }
    
    def test_edge_cases(self):
        """测试边界情况"""
        print("=" * 60)
        print("🔬 测试边界情况")
        print("=" * 60)
        
        monitor = MonitorCore()
        
        # 边界情况1: 相同组织代码，不同名称
        print("\n--- 边界情况1: 相同组织代码，不同名称 ---")
        
        same_code = "EDGE_CASE_001"
        
        # 数据库中的记录
        db_record = {
            'aaae0102': same_code,
            'aaae0103': '常州市长荡湖高级中学',  # 原名称
            'aaae0113': 'area1',
            'aaae0123': 'type1'
        }
        
        # 网站中的记录（名称略有不同）
        website_record = {
            'aaae0102': same_code,
            'aaae0103': '常州市长荡湖高级中学（新）',  # 名称变化
            'aaae0113': 'area1',
            'aaae0123': 'type1'
        }
        
        # 添加到数据库
        self.db_manager.insert_data(self.target_keyword, [db_record])
        
        # 同步测试
        sync_result = monitor._sync_database_with_website(self.target_keyword, [website_record])
        print(f"🔄 同步结果:")
        print(f"   新增: {len(sync_result['new_data'])} 条")
        print(f"   删除: {len(sync_result['deleted_data'])} 条")
        
        # 边界情况2: 名称相同，不同组织代码
        print("\n--- 边界情况2: 名称相同，不同组织代码 ---")
        
        # 网站中的记录（代码不同）
        website_record2 = {
            'aaae0102': 'EDGE_CASE_002',  # 不同代码
            'aaae0103': '常州市长荡湖高级中学',  # 相同名称
            'aaae0113': 'area2',
            'aaae0123': 'type2'
        }
        
        sync_result2 = monitor._sync_database_with_website(self.target_keyword, [website_record2])
        print(f"🔄 同步结果:")
        print(f"   新增: {len(sync_result2['new_data'])} 条")
        print(f"   删除: {len(sync_result2['deleted_data'])} 条")
        
        # 清理测试数据
        self.db_manager.delete_data(self.target_keyword, [db_record])
        self.db_manager.delete_data(self.target_keyword, [website_record2])
        
        return {
            'same_code_diff_name': sync_result,
            'same_name_diff_code': sync_result2
        }
    
    def analyze_notification_trigger_conditions(self):
        """分析通知触发条件"""
        print("=" * 60)
        print("📢 分析通知触发条件")
        print("=" * 60)
        
        print("📋 当前通知触发逻辑:")
        print("1. 只有当sync_result['new_data']不为空时才发送通知")
        print("2. 新增数据的判断基于组织代码(aaae0102)的唯一性")
        print("3. 如果组织代码在数据库中不存在，则认为是新增")
        
        print("\n🔍 可能导致重复通知的原因:")
        print("1. 网站数据的组织代码发生变化")
        print("2. 数据获取过程中的不一致性")
        print("3. 数据库同步过程中的异常")
        print("4. 组织代码的格式问题（空格、大小写等）")
        print("5. 网站数据的临时波动")
        
        # 检查数据库中是否有格式问题
        all_data = self.db_manager.get_all_data(self.target_keyword)
        
        print(f"\n🔧 数据格式检查:")
        code_issues = []
        name_issues = []
        
        for record in all_data:
            code = record.get('aaae0102', '')
            name = record.get('aaae0103', '')
            
            # 检查代码格式问题
            if code != code.strip():
                code_issues.append(f"代码有空格: '{code}'")
            
            # 检查名称格式问题
            if name != name.strip():
                name_issues.append(f"名称有空格: '{name}'")
            
            # 检查目标学校的代码
            if self.target_school in name:
                print(f"   目标学校代码: '{code}' (长度: {len(code)})")
                print(f"   目标学校名称: '{name}' (长度: {len(name)})")
        
        if code_issues:
            print(f"⚠️ 发现 {len(code_issues)} 个代码格式问题")
        if name_issues:
            print(f"⚠️ 发现 {len(name_issues)} 个名称格式问题")
        
        if not code_issues and not name_issues:
            print("✅ 未发现明显的格式问题")
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🚀 开始分析'常州市长荡湖高级中学'重复通知问题")
        print("时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        print()
        
        # 1. 检查当前数据库状态
        db_state = self.check_current_database_state()
        
        # 2. 模拟各种数据场景
        scenarios = self.simulate_data_scenarios()
        
        # 3. 测试边界情况
        edge_cases = self.test_edge_cases()
        
        # 4. 分析通知触发条件
        self.analyze_notification_trigger_conditions()
        
        # 5. 总结分析结果
        print("=" * 60)
        print("📋 问题分析总结")
        print("=" * 60)
        
        all_data, existing_codes, target_records = db_state
        
        print(f"🔍 关键发现:")
        print(f"1. 数据库中目标学校记录数: {len(target_records)}")
        print(f"2. 数据库总记录数: {len(all_data)}")
        print(f"3. 唯一代码数: {len(existing_codes)}")
        
        print(f"\n📊 场景测试结果:")
        print(f"- 场景1(已存在): 被识别为新增 = {scenarios['scenario1']['target_in_new']}")
        print(f"- 场景2(不存在): 被识别为新增 = {scenarios['scenario2']['target_in_new']}")
        print(f"- 场景3(被删除): 被识别为删除 = {scenarios['scenario3']['target_in_deleted']}")
        
        print(f"\n🎯 最可能的重复通知原因:")
        if len(target_records) == 0:
            print("1. ⚠️ 目标学校在数据库中不存在，每次获取都被识别为新增")
            print("2. 可能是组织代码在网站上发生了变化")
            print("3. 可能是数据获取过程中的过滤条件问题")
        elif len(target_records) > 1:
            print("1. ⚠️ 目标学校在数据库中有多条记录")
            print("2. 可能存在数据重复插入的问题")
        else:
            print("1. 数据库状态正常，问题可能在数据获取或同步过程中")
            print("2. 建议检查网站数据的实时变化")
        
        print(f"\n💡 建议的解决方案:")
        print("1. 添加详细的日志记录，跟踪每次数据获取的具体内容")
        print("2. 在数据同步前后记录目标学校的状态变化")
        print("3. 添加数据一致性检查机制")
        print("4. 考虑添加通知去重机制（基于时间窗口）")

if __name__ == "__main__":
    analyzer = DuplicateLogicAnalyzer()
    analyzer.run_comprehensive_analysis()
