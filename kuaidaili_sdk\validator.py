"""
代理验证器
"""
import requests
import time
from typing import Dict, Optional, List
from .exceptions import ValidationException


class ProxyValidator:
    """代理验证器类"""
    
    def __init__(self, timeout: int = 8, test_urls: Optional[List[str]] = None):
        """
        初始化验证器
        
        Args:
            timeout: 验证超时时间（秒）
            test_urls: 测试URL列表，默认使用百度
        """
        self.timeout = timeout
        self.test_urls = test_urls or [
            'http://www.baidu.com',
        ]
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def validate_proxy(self, proxy: str, test_url: Optional[str] = None) -> bool:
        """
        验证单个代理是否可用
        
        Args:
            proxy: 代理地址，格式如 "ip:port" 或 "http://ip:port"
            test_url: 测试URL，默认使用百度
            
        Returns:
            bool: 代理是否可用
        """
        try:
            proxy_config = self._format_proxy(proxy)
            test_url = test_url or self.test_urls[0]
            
            # 先进行快速连接测试
            if not self._quick_test(proxy_config, test_url):
                return False
            
            # 再进行完整测试
            return self._full_test(proxy_config, test_url)
            
        except Exception as e:
            print(f"❌ 代理验证异常: {e}")
            return False
    
    def validate_proxy_list(self, proxies: List[str], 
                          max_workers: int = 5) -> Dict[str, bool]:
        """
        批量验证代理列表
        
        Args:
            proxies: 代理列表
            max_workers: 最大并发数
            
        Returns:
            Dict[str, bool]: 代理验证结果字典
        """
        results = {}
        
        # 简单的顺序验证（可以后续优化为并发）
        for proxy in proxies:
            results[proxy] = self.validate_proxy(proxy)
            
        return results
    
    def get_valid_proxies(self, proxies: List[str]) -> List[str]:
        """
        从代理列表中筛选出可用的代理
        
        Args:
            proxies: 代理列表
            
        Returns:
            List[str]: 可用的代理列表
        """
        valid_proxies = []
        
        for proxy in proxies:
            if self.validate_proxy(proxy):
                valid_proxies.append(proxy)
                
        return valid_proxies
    
    def _format_proxy(self, proxy: str) -> Dict[str, str]:
        """格式化代理配置"""
        if not proxy:
            raise ValidationException("代理地址不能为空")
        
        # 确保代理地址包含协议
        if '://' not in proxy:
            proxy = f'http://{proxy}'
        
        return {
            'http': proxy,
            'https': proxy
        }
    
    def _quick_test(self, proxy_config: Dict[str, str], test_url: str) -> bool:
        """快速连接测试"""
        try:
            response = requests.head(
                test_url,
                proxies=proxy_config,
                timeout=min(self.timeout, 5),  # 快速测试使用较短超时
                headers=self.headers
            )
            return response.status_code in [200, 301, 302]
        except:
            return False
    
    def _full_test(self, proxy_config: Dict[str, str], test_url: str) -> bool:
        """完整功能测试"""
        try:
            response = requests.get(
                test_url,
                proxies=proxy_config,
                timeout=self.timeout,
                headers=self.headers
            )
            
            if response.status_code == 200:
                # 检查响应内容
                content = response.text.lower()
                
                # 根据测试URL验证响应内容
                if 'baidu.com' in test_url:
                    return 'baidu' in content or '百度' in content
                elif 'httpbin.org' in test_url:
                    return 'origin' in content  # httpbin返回IP信息
                elif 'google.com' in test_url:
                    return 'google' in content
                else:
                    return True  # 其他URL只检查状态码
            
            return False
            
        except requests.exceptions.ConnectTimeout:
            print("❌ 代理验证失败: 连接超时")
            return False
        except requests.exceptions.ReadTimeout:
            print("❌ 代理验证失败: 读取超时")
            return False
        except requests.exceptions.ProxyError as e:
            print(f"❌ 代理验证失败: 代理错误 - {str(e)}")
            return False
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 代理验证失败: 连接错误 - {str(e)}")
            return False
        except Exception as e:
            print(f"❌ 代理验证失败: {str(e)}")
            return False
