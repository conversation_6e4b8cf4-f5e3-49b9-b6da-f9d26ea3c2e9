"""
监控核心模块
"""
from PyQt5.QtCore import QThread, pyqtSignal
import time
import random
from datetime import datetime

from core.crawler import Crawler<PERSON>ore
from core.database import DatabaseManager
from core.notification import NotificationManager
from core.proxy_manager import ProxyManager
from utils.resource_manager import ResourceManager
from utils.config import ConfigManager


class MonitorCore(QThread):
    """监控核心类"""

    # 信号定义
    new_data_found = pyqtSignal(str, list)  # 关键词, 新数据列表
    status_changed = pyqtSignal(str)        # 状态变化
    log_message = pyqtSignal(str)           # 日志消息

    def __init__(self):
        super().__init__()
        self.running = False
        self.active_keywords = []

        # 初始化各个管理器
        self.resource_manager = ResourceManager()
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager()
        self.notification_manager = NotificationManager()
        self.proxy_manager = ProxyManager(self.config_manager)  # 传递config_manager
        self.crawler = CrawlerCore(self.resource_manager, self.proxy_manager, self.config_manager)

    def start_monitor(self, keywords):
        """开始监控"""
        # 检查是否已经在运行
        if self.running or self.isRunning():
            self.log_message.emit("⚠️ 监控已在运行中，拒绝重复启动")
            return False

        # 检查代理配置
        proxy_enabled = self.config_manager.is_proxy_enabled()
        kuaidaili_enabled = self.config_manager.is_kuaidaili_enabled()

        if not proxy_enabled:
            self.log_message.emit("❌ 监控启动失败：需启用代理才能开始监控")
            self.log_message.emit("💡 请在设置中启用代理功能")
            return False

        if not kuaidaili_enabled:
            self.log_message.emit("❌ 监控启动失败：需配置快代理API才能开始监控")
            self.log_message.emit("💡 请在代理设置中配置快代理的Secret ID和Signature")
            return False

        # 测试代理API连接
        self.log_message.emit("🔍 检查快代理API连接...")
        success, message = self.proxy_manager.kuaidaili_manager.test_api_connection()
        if not success:
            self.log_message.emit(f"❌ 监控启动失败：快代理API连接失败 - {message}")
            self.log_message.emit("💡 请检查快代理凭证是否正确")
            return False

        self.log_message.emit("✅ 快代理API连接正常")

        self.active_keywords = keywords
        self.running = True
        self.proxy_prepared = False  # 添加标志，避免重复获取代理

        # 只在首次启动或资源未初始化时才初始化资源管理器
        if not self.resource_manager.is_initialized():
            self.log_message.emit("🔄 初始化运行时资源...")
            if not self.resource_manager.initialize_runtime():
                self.log_message.emit("❌ 资源初始化失败，监控无法启动")
                self.running = False
                return False

            # 初始化JavaScript执行器
            self.log_message.emit("🔄 初始化JavaScript执行环境...")
            self._init_js_executor()
        else:
            self.log_message.emit("✅ 运行时资源已就绪")

        # 加载配置
        self._load_config()

        self.start()
        return True

    def stop_monitor(self):
        """停止监控"""
        if not self.running:
            return

        self.running = False

        # 等待线程结束，最多等待5秒
        if self.isRunning():
            self.wait(5000)  # 等待5秒
            if self.isRunning():
                self.log_message.emit("⚠️ 监控线程停止超时，强制终止")
                self.terminate()
                self.wait(1000)  # 再等待1秒

    def _load_config(self):
        """加载配置"""
        # 加载webhook配置
        webhook_url = self.config_manager.get_webhook_url()
        self.notification_manager.set_webhook_url(webhook_url)

        # 代理配置现在通过快代理API自动管理，无需手动加载

    def _reload_proxy_config(self):
        """重新加载代理配置（实时生效）"""
        proxy_enabled = self.config_manager.is_proxy_enabled()
        kuaidaili_enabled = self.config_manager.is_kuaidaili_enabled()

        # 更新代理管理器的配置
        self.proxy_manager.update_config()

        self.log_message.emit(f"🔍 代理配置检查: 启用={proxy_enabled}, 快代理API={kuaidaili_enabled}")

        if proxy_enabled and kuaidaili_enabled:
            self.log_message.emit("🔄 代理已启用，将使用快代理API自动获取代理")
        elif proxy_enabled and not kuaidaili_enabled:
            self.log_message.emit("⚠️ 代理已启用但快代理API未配置，请配置快代理凭证")
        else:
            self.log_message.emit("🔄 代理已禁用，切换到直连模式")

    def _prepare_proxy_for_cycle(self):
        """为本次检查周期准备代理（整个周期内重复使用同一个代理）"""
        # 如果已经准备过代理，直接返回
        if hasattr(self, 'proxy_prepared') and self.proxy_prepared:
            if (hasattr(self.proxy_manager, 'current_proxy') and
                self.proxy_manager.current_proxy):
                self.log_message.emit(f"✅ 继续使用已验证的代理: {self.proxy_manager.current_proxy}")
            else:
                self.log_message.emit("🔄 继续使用直连模式")
            return

        proxy_enabled = self.config_manager.is_proxy_enabled()
        kuaidaili_enabled = self.config_manager.is_kuaidaili_enabled()

        if proxy_enabled and kuaidaili_enabled:
            # 获取经过验证的新代理供本次检查周期使用
            self.log_message.emit("🔄 获取并验证代理...")

            proxy_config = self.proxy_manager.get_verified_proxy(max_attempts=5)
            if proxy_config:
                self.log_message.emit(f"✅ 本次检查周期使用已验证的代理: {self.proxy_manager.current_proxy}")
            else:
                self.log_message.emit("❌ 无法获取可用代理，本次检查周期使用直连模式")
                self.log_message.emit("💡 建议检查：1) 网络连接 2) 快代理API凭证 3) 快代理账户余额")

        elif proxy_enabled and not kuaidaili_enabled:
            self.log_message.emit("⚠️ 代理已启用但快代理API未配置，本次检查周期使用直连模式")
        else:
            self.log_message.emit("🔄 代理已禁用，本次检查周期使用直连模式")

        # 标记代理已准备
        self.proxy_prepared = True

    def run(self):
        """主监控循环"""
        self.log_message.emit("监控线程启动")

        # 显示监控时间段信息
        start_time_str, end_time_str = self.config_manager.get_monitor_time()
        self.log_message.emit(f"⏰ 监控时间段: {start_time_str} - {end_time_str}")

        self.status_changed.emit("运行中")

        while self.running:
            try:
                # 检查是否在监控时间段内
                if not self._is_monitor_time():
                    self.log_message.emit("当前不在监控时间段内，等待中...")
                    time.sleep(60)  # 等待1分钟后再检查
                    continue

                # 在检查周期开始时获取一次代理，整个周期内重复使用
                self._prepare_proxy_for_cycle()

                for keyword in self.active_keywords:
                    if not self.running:
                        break
                    # 添加检测分隔线
                    self.log_message.emit("------------------------------------------")
                    self._check_keyword(keyword)

                if self.running:
                    # 随机等待5-6分钟
                    self._random_sleep()

            except Exception as e:
                self.log_message.emit(f"监控过程中发生错误: {str(e)}")
                time.sleep(30)  # 出错后等待30秒

        # self.log_message.emit("监控线程停止")
        self.status_changed.emit("已停止")

    def _is_monitor_time(self):
        """检查是否在监控时间段内"""
        start_time_str, end_time_str = self.config_manager.get_monitor_time()

        try:
            start_time = datetime.strptime(start_time_str, '%H:%M').time()
            end_time = datetime.strptime(end_time_str, '%H:%M').time()
            current_time = datetime.now().time()

            if start_time <= end_time:
                # 同一天内的时间段
                return start_time <= current_time <= end_time
            else:
                # 跨天的时间段
                return current_time >= start_time or current_time <= end_time

        except ValueError:
            # 时间格式错误，默认允许监控
            return True

    def _check_keyword(self, keyword):
        """检查单个关键词"""
        self.log_message.emit(f"开始检查关键词: {keyword}")
        self.log_message.emit(f"🔗 正在连接服务器获取验证码...")

        # 每次检查时重新加载代理配置，确保实时生效
        # self.log_message.emit("🔄 检查代理配置...")
        self._reload_proxy_config()

        try:
            # 获取数据库中的实际记录数量
            last_count = self.db_manager.get_record_count(keyword)
            self.log_message.emit(f"💾 数据库中现有 {keyword} 记录: {last_count} 条")

            # 先用小页面大小检查总数，支持代理重新获取
            result = self.crawler.try_with_retry(keyword, page_size=10, max_retries=10, max_proxy_cycles=3)

            if result[0] is None:
                self.log_message.emit(f"❌ 获取 {keyword} 数据失败 - 已尝试多个代理周期仍然失败")
                return

            data, success_attempt, total_count = result
            _ = success_attempt  # 忽略成功尝试次数
            self.log_message.emit(f"✅ 数据获取成功！")

            # 显示获取到的数据示例作为真实性验证
            if data and len(data) > 0:
                first_org = data[0].get('aaae0103', '未知机构').strip()
                _ = first_org  # 暂时不显示机构名称
                # self.log_message.emit(f"📋 最新数据示例: {first_org}")

            self.log_message.emit(f"📊 对比结果: 网站总数 {total_count} vs 数据库记录 {last_count}")

            # 检查数据是否有变化（不管是增加、减少还是总数相同）
            if total_count != last_count:
                # 数量不一致，需要详细对比
                if total_count > last_count:
                    self.log_message.emit(f"📈 {keyword} 网站数据增加! 网站 {total_count} 条 vs 数据库 {last_count} 条")
                elif total_count < last_count:
                    self.log_message.emit(f"📉 {keyword} 网站数据减少! 网站 {total_count} 条 vs 数据库 {last_count} 条")

                self.log_message.emit(f"🔄 数据不一致，重新获取完整数据进行详细对比...")

                # 用大页面大小重新获取数据，支持代理重新获取
                result = self.crawler.try_with_retry(keyword, page_size=200, max_retries=10, max_proxy_cycles=3)
                if result[0] is not None:
                    data, _, _ = result
                    self.log_message.emit(f"✅ 完整数据获取成功，共 {len(data)} 条记录")

                    # 进行完整的数据同步对比
                    sync_result = self._sync_database_with_website(keyword, data)

                    new_data = sync_result['new_data']
                    deleted_data = sync_result['deleted_data']

                    # 处理新增数据
                    if new_data:
                        self.log_message.emit(f"🎉 发现 {len(new_data)} 条新增数据:")
                        for i, item in enumerate(new_data[:3]):  # 只显示前3条
                            org_name = item.get('aaae0103', '').strip()
                            org_code = item.get('aaae0102', '').strip()
                            self.log_message.emit(f"   {i+1}. {org_name} ({org_code})")

                        if len(new_data) > 3:
                            self.log_message.emit(f"   ... 还有 {len(new_data) - 3} 条新增数据")

                        # 保存新数据到数据库
                        inserted_count = self.db_manager.insert_data(keyword, new_data)
                        self.log_message.emit(f"💾 新增 {inserted_count} 条数据到数据库")

                        # 发送通知（只有新增才发通知）
                        if self.config_manager.is_notification_enabled():
                            self.log_message.emit(f"📤 正在发送企业微信通知...")
                            success, msg = self.notification_manager.send_notification(keyword, new_data)
                            if success:
                                self.log_message.emit("✅ 企业微信通知发送成功")
                            else:
                                self.log_message.emit(f"❌ 企业微信通知发送失败: {msg}")

                        # 发送信号
                        self.new_data_found.emit(keyword, new_data)

                    # 处理删除数据（只记录日志，不发通知）
                    if deleted_data:
                        self.log_message.emit(f"📉 发现 {len(deleted_data)} 条数据已从网站删除:")
                        for i, item in enumerate(deleted_data[:5]):  # 显示前5条删除的数据
                            org_name = item.get('aaae0103', '').strip()
                            org_code = item.get('aaae0102', '').strip()
                            self.log_message.emit(f"   {i+1}. {org_name} ({org_code}) - 已删除")

                        if len(deleted_data) > 5:
                            self.log_message.emit(f"   ... 还有 {len(deleted_data) - 5} 条数据被删除")

                        # 从数据库中删除这些记录，保持同步
                        deleted_count = self.db_manager.delete_data(keyword, deleted_data)
                        self.log_message.emit(f"🗑️ 从数据库删除 {deleted_count} 条记录，保持数据同步")

                    # 总结同步结果
                    if not new_data and not deleted_data:
                        self.log_message.emit(f"ℹ️ 数量不一致但内容相同，可能是网络波动导致的临时差异")
                    else:
                        self.log_message.emit(f"✅ 数据库同步完成: 新增 {len(new_data)} 条，删除 {len(deleted_data)} 条")
                else:
                    self.log_message.emit(f"❌ 重新获取完整数据失败，无法进行详细对比")
            else:
                # 数量一致，但仍然进行轻量级检查（可选）
                self.log_message.emit(f"✅ {keyword} 数据数量一致，无需详细对比")

            # 更新检查时间
            self.config_manager.set_last_check_time()

        except Exception as e:
            self.log_message.emit(f"检查 {keyword} 时发生错误: {str(e)}")

        # 添加检测结束分隔线
        self.log_message.emit("------------------------------------------")

    def _find_new_data(self, keyword, all_data):
        """找出新增的数据"""
        if not all_data:
            return []

        # 获取数据库中已有的组织代码
        existing_codes = self.db_manager.get_existing_codes(keyword)

        # 找出新增的数据
        new_data = []
        for item in all_data:
            code = item.get('aaae0102', '').strip()
            if code and code not in existing_codes:
                new_data.append(item)

        return new_data

    def _sync_database_with_website(self, keyword, website_data):
        """同步数据库与网站数据，返回新增和删除的数据"""
        if not website_data:
            return {'new_data': [], 'deleted_data': []}

        # 获取网站数据的组织代码集合
        website_codes = set()
        website_data_dict = {}
        for item in website_data:
            code = item.get('aaae0102', '').strip()
            if code:
                website_codes.add(code)
                website_data_dict[code] = item

        # 获取数据库中的所有数据
        db_data = self.db_manager.get_all_data(keyword)
        db_codes = set()
        db_data_dict = {}
        for item in db_data:
            code = item.get('aaae0102', '').strip()
            if code:
                db_codes.add(code)
                db_data_dict[code] = item

        # 找出新增的数据（在网站中但不在数据库中）
        new_codes = website_codes - db_codes
        new_data = [website_data_dict[code] for code in new_codes]

        # 找出删除的数据（在数据库中但不在网站中）
        deleted_codes = db_codes - website_codes
        deleted_data = [db_data_dict[code] for code in deleted_codes]

        return {
            'new_data': new_data,
            'deleted_data': deleted_data
        }

    def _random_sleep(self):
        """随机等待5-6分钟"""
        min_interval, max_interval = self.config_manager.get_monitor_interval()
        sleep_time = random.randint(min_interval * 60, max_interval * 60)  # 转换为秒

        # self.log_message.emit(f"⏰ 使用固定监控间隔: {min_interval}-{max_interval} 分钟")
        self.log_message.emit(f"等待 {sleep_time // 60} 分 {sleep_time % 60} 秒后进行下次检查")

        # 分段睡眠，便于响应停止信号
        for _ in range(sleep_time):
            if not self.running:
                break
            time.sleep(1)

    def _init_js_executor(self):
        """初始化JavaScript执行器"""
        try:
            from utils.js_executor import get_js_executor

            js_executor = get_js_executor()

            if (self.resource_manager.get_node_path() and
                self.resource_manager.get_js_file_path()):

                node_path = self.resource_manager.get_node_path()
                js_file_path = self.resource_manager.get_js_file_path()

                if js_executor.initialize(node_path, js_file_path):
                    self.log_message.emit("✅ JavaScript执行环境初始化成功")
                else:
                    self.log_message.emit("⚠️ JavaScript执行环境初始化失败，将使用备选方案")
            else:
                self.log_message.emit("⚠️ Node.js路径或JS文件路径无效，将使用备选方案")

        except Exception as e:
            self.log_message.emit(f"⚠️ JavaScript执行器初始化出错: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            from utils.js_executor import cleanup_js_executor
            cleanup_js_executor()
        except:
            pass


