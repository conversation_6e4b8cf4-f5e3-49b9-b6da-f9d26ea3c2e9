"""
JavaScript执行管理器
避免每次调用都创建新的Node.js进程
"""

import os
import subprocess
import threading
import time
import json
import tempfile
from pathlib import Path

# 全局subprocess拦截器
_original_popen = subprocess.Popen

def _hidden_popen(*args, **kwargs):
    """全局subprocess.Popen拦截器，确保所有子进程都隐藏窗口"""
    if os.name == 'nt':  # Windows
        if 'startupinfo' not in kwargs:
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            kwargs['startupinfo'] = startupinfo
        else:
            # 如果已有startupinfo，确保设置隐藏窗口
            startupinfo = kwargs['startupinfo']
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

        if 'creationflags' not in kwargs:
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
        else:
            # 如果已有creationflags，添加隐藏窗口标志
            kwargs['creationflags'] |= subprocess.CREATE_NO_WINDOW

    return _original_popen(*args, **kwargs)

# 立即应用全局拦截器
subprocess.Popen = _hidden_popen


class JavaScriptExecutor:
    """JavaScript执行管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self.node_process = None
        self.node_path = None
        self.js_file_path = None
        self.temp_dir = None
        self.process_lock = threading.Lock()
        
    def initialize(self, node_path, js_file_path):
        """初始化JavaScript执行环境"""
        self.node_path = node_path
        self.js_file_path = js_file_path
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp(prefix='js_executor_')
        
        # 启动持久化的Node.js进程
        return self._start_node_process()
    
    def _start_node_process(self):
        """启动Node.js进程"""
        try:
            if self.node_process and self.node_process.poll() is None:
                return True
                
            # 创建Node.js服务器脚本
            server_script = self._create_server_script()
            
            # 启动Node.js进程，隐藏窗口
            startupinfo = None
            creationflags = 0
            
            if os.name == 'nt':  # Windows
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW
            
            self.node_process = subprocess.Popen(
                [self.node_path, server_script],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                startupinfo=startupinfo,
                creationflags=creationflags,
                cwd=self.temp_dir
            )
            
            # 等待进程启动
            time.sleep(0.5)
            
            if self.node_process.poll() is None:
                print("✅ Node.js执行环境启动成功")
                return True
            else:
                print("❌ Node.js执行环境启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动Node.js进程失败: {e}")
            return False
    
    def _create_server_script(self):
        """创建Node.js服务器脚本"""
        server_script_path = os.path.join(self.temp_dir, 'js_server.js')
        
        # 读取原始JavaScript文件内容
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 创建服务器脚本
        server_code = f'''
const readline = require('readline');

// 加载原始JavaScript代码
{js_content}

// 创建readline接口
const rl = readline.createInterface({{
    input: process.stdin,
    output: process.stdout
}});

console.log("JS_SERVER_READY");

// 处理输入命令
rl.on('line', (input) => {{
    try {{
        const request = JSON.parse(input);
        const {{ method, args }} = request;
        
        let result;
        if (method === 'get_params') {{
            result = get_params(args[0], args[1], args[2]);
        }} else {{
            throw new Error(`Unknown method: ${{method}}`);
        }}
        
        console.log(JSON.stringify({{ success: true, result: result }}));
    }} catch (error) {{
        console.log(JSON.stringify({{ success: false, error: error.message }}));
    }}
}});

// 处理进程退出
process.on('SIGINT', () => {{
    rl.close();
    process.exit(0);
}});
'''
        
        with open(server_script_path, 'w', encoding='utf-8') as f:
            f.write(server_code)
        
        return server_script_path
    
    def execute_function(self, function_name, *args):
        """执行JavaScript函数"""
        with self.process_lock:
            try:
                if not self.node_process or self.node_process.poll() is not None:
                    if not self._start_node_process():
                        return None, "Node.js进程启动失败"
                
                # 构建请求
                request = {
                    'method': function_name,
                    'args': list(args)
                }
                
                # 发送请求
                request_json = json.dumps(request) + '\n'
                self.node_process.stdin.write(request_json)
                self.node_process.stdin.flush()
                
                # 读取响应
                response_line = self.node_process.stdout.readline().strip()
                
                if not response_line:
                    return None, "未收到响应"
                
                response = json.loads(response_line)
                
                if response.get('success'):
                    return response.get('result'), None
                else:
                    return None, response.get('error', '未知错误')
                    
            except Exception as e:
                return None, f"执行JavaScript函数失败: {e}"
    
    def get_params(self, a, b, xcode):
        """获取加密参数"""
        return self.execute_function('get_params', a, b, xcode)
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.node_process and self.node_process.poll() is None:
                self.node_process.terminate()
                self.node_process.wait(timeout=5)
        except:
            pass
        
        try:
            if self.temp_dir and os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir)
        except:
            pass
    
    def __del__(self):
        """析构函数"""
        self.cleanup()


# 全局JavaScript执行器实例
_js_executor = None

def get_js_executor():
    """获取JavaScript执行器实例"""
    global _js_executor
    if _js_executor is None:
        _js_executor = JavaScriptExecutor()
    return _js_executor

def cleanup_js_executor():
    """清理JavaScript执行器"""
    global _js_executor
    if _js_executor:
        _js_executor.cleanup()
        _js_executor = None
