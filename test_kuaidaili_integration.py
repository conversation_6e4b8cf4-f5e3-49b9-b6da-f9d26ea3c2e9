"""
快代理SDK集成测试
使用项目中的真实配置进行测试
"""
import sys
import os

# 添加SDK路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'kuaidaili_sdk'))

from kuaidaili_sdk import KuaidailiClient, ProxyPool, KuaidailiConfig
from kuaidaili_sdk.exceptions import APIException, ProxyException, ConfigException


def test_with_project_config():
    """使用项目配置进行测试"""
    print("=== 使用项目配置测试快代理SDK ===")
    
    config_file = "config.ini"
    
    if not os.path.exists(config_file):
        print("❌ 未找到config.ini配置文件")
        return False
    
    try:
        # 从项目配置文件创建客户端
        print("🔄 从配置文件创建客户端...")
        client = KuaidailiClient.from_config_file(config_file)
        print("✅ 客户端创建成功")
        
        # 测试API连接
        print("🔄 测试API连接...")
        success, message = client.test_connection()
        print(f"连接测试结果: {message}")
        
        if not success:
            print("❌ API连接失败，无法继续测试")
            return False
        
        # 获取单个代理
        print("🔄 获取单个代理...")
        proxy = client.get_proxy(validate=False)
        if proxy:
            print(f"✅ 成功获取代理: {proxy}")
        else:
            print("❌ 获取代理失败")
            return False
        
        # 验证代理
        print("🔄 验证代理可用性...")
        is_valid = client.validate_proxy(proxy)
        print(f"代理验证结果: {'✅ 可用' if is_valid else '❌ 不可用'}")
        
        # 获取验证过的代理
        print("🔄 获取验证过的代理...")
        verified_proxy = client.get_verified_proxy(max_attempts=3)
        if verified_proxy:
            print(f"✅ 成功获取验证代理: {verified_proxy}")
        else:
            print("⚠️ 未能获取验证代理")
        
        # 获取代理列表
        print("🔄 获取代理列表...")
        proxy_list = client.get_proxy_list(num=3, validate=False)
        print(f"✅ 获取到 {len(proxy_list)} 个代理")
        for i, p in enumerate(proxy_list, 1):
            print(f"  {i}. {p}")
        
        # 测试代理池
        print("🔄 测试代理池...")
        try:
            proxy_pool = ProxyPool(
                client=client,
                pool_size=5,
                auto_refresh=False  # 关闭自动刷新，避免频繁API调用
            )
            
            # 获取代理池状态
            status = proxy_pool.get_pool_status()
            print(f"✅ 代理池状态: {status}")
            
            # 从代理池获取代理
            pool_proxy = proxy_pool.get_proxy(validate=False)
            if pool_proxy:
                print(f"✅ 从代理池获取代理: {pool_proxy}")
            
        except Exception as e:
            print(f"⚠️ 代理池测试异常: {e}")
        
        print("✅ 所有测试完成")
        return True
        
    except APIException as e:
        print(f"❌ API异常: {e}")
        return False
    except ConfigException as e:
        print(f"❌ 配置异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他异常: {e}")
        return False


def test_sdk_basic_functions():
    """测试SDK基本功能（不需要真实API）"""
    print("\n=== 测试SDK基本功能 ===")
    
    try:
        # 测试配置创建
        config = KuaidailiConfig(
            secret_id="test_id",
            signature="test_signature"
        )
        assert config.is_valid()
        print("✅ 配置创建测试通过")
        
        # 测试客户端创建
        client = KuaidailiClient(config=config)
        assert client.config.secret_id == "test_id"
        print("✅ 客户端创建测试通过")
        
        # 测试代理格式化
        proxy_config = client.get_proxy_config("192.168.1.1:8080")
        expected = {
            'http': 'http://192.168.1.1:8080',
            'https': 'http://192.168.1.1:8080'
        }
        assert proxy_config == expected
        print("✅ 代理格式化测试通过")
        
        # 测试验证器
        from kuaidaili_sdk.validator import ProxyValidator
        validator = ProxyValidator()
        
        # 测试无效代理（应该快速失败）
        is_valid = validator.validate_proxy("127.0.0.1:9999")
        assert is_valid == False
        print("✅ 代理验证测试通过")
        
        print("✅ 基本功能测试全部通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("快代理SDK集成测试")
    print("=" * 50)
    
    # 测试基本功能
    basic_success = test_sdk_basic_functions()
    
    # 测试真实API（如果配置可用）
    api_success = test_with_project_config()
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"基本功能测试: {'✅ 通过' if basic_success else '❌ 失败'}")
    print(f"API集成测试: {'✅ 通过' if api_success else '❌ 失败'}")
    
    if basic_success and api_success:
        print("🎉 所有测试通过！快代理SDK可以正常使用")
    elif basic_success:
        print("⚠️ 基本功能正常，但API测试失败（可能是配置或网络问题）")
    else:
        print("❌ 测试失败，请检查SDK实现")


if __name__ == "__main__":
    main()
