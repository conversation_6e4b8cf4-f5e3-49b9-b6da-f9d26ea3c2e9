#!/usr/bin/env python3
"""
测试代理获取逻辑，确保不重复获取
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.config import ConfigManager
from core.proxy_manager import ProxyManager
from core.kuaidaili_manager import <PERSON><PERSON><PERSON><PERSON>Mana<PERSON>

def test_proxy_logic():
    """测试代理获取逻辑"""
    print("🧪 测试代理获取逻辑...")
    print("=" * 60)
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 检查代理配置
    proxy_enabled = config_manager.is_proxy_enabled()
    kuaidaili_enabled = config_manager.is_kuaidaili_enabled()
    
    print(f"📋 代理配置状态:")
    print(f"   代理启用: {proxy_enabled}")
    print(f"   快代理API启用: {kuaidaili_enabled}")
    
    if not proxy_enabled or not kuaidaili_enabled:
        print("⚠️ 代理或快代理API未启用，无法测试")
        return
    
    # 初始化组件
    kuaidaili_manager = <PERSON>aidailiManager(config_manager)
    proxy_manager = ProxyManager(config_manager, kuaidaili_manager)
    
    print("\n🔍 测试代理获取流程...")
    
    # 第一次获取代理
    print("\n1️⃣ 第一次获取代理:")
    proxy_config = proxy_manager.get_verified_proxy(max_attempts=3)
    
    if proxy_config:
        print(f"✅ 第一次获取成功: {proxy_manager.current_proxy}")
        current_proxy_1 = proxy_manager.current_proxy
    else:
        print("❌ 第一次获取失败")
        return
    
    # 模拟监控核心的逻辑
    print("\n2️⃣ 模拟监控核心检查已有代理:")
    if (hasattr(proxy_manager, 'current_proxy') and 
        proxy_manager.current_proxy):
        print(f"✅ 发现已有代理: {proxy_manager.current_proxy}")
        print("💡 应该直接使用，不再重新获取")
        should_reuse = True
    else:
        print("❌ 没有发现已有代理")
        should_reuse = False
    
    # 第二次获取代理（应该复用）
    print("\n3️⃣ 第二次获取代理（测试是否复用）:")
    if should_reuse:
        print("✅ 复用已有代理，不进行新的获取")
        current_proxy_2 = proxy_manager.current_proxy
    else:
        proxy_config = proxy_manager.get_verified_proxy(max_attempts=3)
        if proxy_config:
            print(f"✅ 第二次获取成功: {proxy_manager.current_proxy}")
            current_proxy_2 = proxy_manager.current_proxy
        else:
            print("❌ 第二次获取失败")
            return
    
    # 对比结果
    print(f"\n📊 结果对比:")
    print(f"   第一次代理: {current_proxy_1}")
    print(f"   第二次代理: {current_proxy_2}")
    
    if current_proxy_1 == current_proxy_2:
        print("✅ 代理复用成功，没有重复获取")
    else:
        print("❌ 代理没有复用，发生了重复获取")
    
    # 测试强制重新获取
    print(f"\n4️⃣ 测试强制重新获取:")
    print("清除当前代理状态...")
    proxy_manager.current_proxy = None
    
    proxy_config = proxy_manager.get_verified_proxy(max_attempts=3)
    if proxy_config:
        print(f"✅ 强制重新获取成功: {proxy_manager.current_proxy}")
        current_proxy_3 = proxy_manager.current_proxy
        
        if current_proxy_3 != current_proxy_1:
            print("✅ 获取到了新的代理IP")
        else:
            print("ℹ️ 获取到了相同的代理IP（正常情况）")
    else:
        print("❌ 强制重新获取失败")

def simulate_monitor_logic():
    """模拟监控核心的代理准备逻辑"""
    print("\n" + "=" * 60)
    print("🧪 模拟监控核心的代理准备逻辑...")
    
    config_manager = ConfigManager()
    kuaidaili_manager = KuaidailiManager(config_manager)
    proxy_manager = ProxyManager(config_manager, kuaidaili_manager)
    
    def prepare_proxy_for_cycle():
        """模拟 _prepare_proxy_for_cycle 方法"""
        proxy_enabled = config_manager.is_proxy_enabled()
        kuaidaili_enabled = config_manager.is_kuaidaili_enabled()

        if proxy_enabled and kuaidaili_enabled:
            # 检查是否已有可用代理
            if (hasattr(proxy_manager, 'current_proxy') and 
                proxy_manager.current_proxy):
                print(f"✅ 使用已验证的代理: {proxy_manager.current_proxy}")
                return True
            
            # 获取经过验证的新代理供本次检查周期使用
            print("🔄 获取并验证代理...")
            
            proxy_config = proxy_manager.get_verified_proxy(max_attempts=3)
            if proxy_config:
                print(f"✅ 本次检查周期使用已验证的代理: {proxy_manager.current_proxy}")
                return True
            else:
                print("❌ 无法获取可用代理，本次检查周期使用直连模式")
                return False
        else:
            print("🔄 代理未启用，使用直连模式")
            return False
    
    # 第一次调用
    print("\n1️⃣ 第一次调用 prepare_proxy_for_cycle:")
    success1 = prepare_proxy_for_cycle()
    
    # 第二次调用（应该复用）
    print("\n2️⃣ 第二次调用 prepare_proxy_for_cycle:")
    success2 = prepare_proxy_for_cycle()
    
    # 第三次调用（应该复用）
    print("\n3️⃣ 第三次调用 prepare_proxy_for_cycle:")
    success3 = prepare_proxy_for_cycle()
    
    print(f"\n📊 模拟结果:")
    print(f"   第一次: {'成功' if success1 else '失败'}")
    print(f"   第二次: {'成功' if success2 else '失败'}")
    print(f"   第三次: {'成功' if success3 else '失败'}")
    
    if success1 and success2 and success3:
        print("✅ 监控核心代理逻辑正常，避免了重复获取")
    else:
        print("❌ 监控核心代理逻辑有问题")

if __name__ == "__main__":
    try:
        test_proxy_logic()
        simulate_monitor_logic()
        print("\n🎉 代理逻辑测试完成！")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
