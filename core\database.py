"""
数据库管理模块
"""
import sqlite3
import json


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, db_path="social_org_monitor.db"):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建高级中学表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS gaoji_zhongxue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                aaae0102 TEXT UNIQUE NOT NULL,
                aaae0103 TEXT NOT NULL,
                aaae0113 TEXT,
                aaae0123 TEXT,
                raw_data TEXT,
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建中学表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS zhongxue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                aaae0102 TEXT UNIQUE NOT NULL,
                aaae0103 TEXT NOT NULL,
                aaae0113 TEXT,
                aaae0123 TEXT,
                raw_data TEXT,
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_gaoji_code ON gaoji_zhongxue(aaae0102)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_zhong_code ON zhongxue(aaae0102)')
        
        conn.commit()
        conn.close()
        
    def get_table_name(self, keyword):
        """根据关键词获取表名"""
        if keyword == "高级中学":
            return "gaoji_zhongxue"
        elif keyword == "中学":
            return "zhongxue"
        else:
            raise ValueError(f"不支持的关键词: {keyword}")
            
    def insert_data(self, keyword, data_list):
        """插入数据"""
        table_name = self.get_table_name(keyword)
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        for data in data_list:
            try:
                cursor.execute(f'''
                    INSERT OR IGNORE INTO {table_name} 
                    (aaae0102, aaae0103, aaae0113, aaae0123, raw_data)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    data.get('aaae0102', ''),
                    data.get('aaae0103', ''),
                    data.get('aaae0113', ''),
                    data.get('aaae0123', ''),
                    json.dumps(data, ensure_ascii=False)
                ))
                if cursor.rowcount > 0:
                    inserted_count += 1
            except Exception as e:
                print(f"插入数据失败: {e}")
                
        conn.commit()
        conn.close()
        return inserted_count
        
    def get_existing_codes(self, keyword):
        """获取已存在的组织代码列表"""
        table_name = self.get_table_name(keyword)
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f'SELECT aaae0102 FROM {table_name}')
        codes = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        return set(codes)
        
    def get_record_count(self, keyword):
        """获取记录总数"""
        table_name = self.get_table_name(keyword)
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
        count = cursor.fetchone()[0]

        conn.close()
        return count

    def get_all_data(self, keyword):
        """获取所有数据"""
        table_name = self.get_table_name(keyword)
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(f'''
            SELECT aaae0102, aaae0103, aaae0113, aaae0123, raw_data
            FROM {table_name}
        ''')

        data_list = []
        for row in cursor.fetchall():
            try:
                # 尝试从raw_data解析完整数据
                if row[4]:
                    data = json.loads(row[4])
                else:
                    # 如果没有raw_data，构造基本数据
                    data = {
                        'aaae0102': row[0],
                        'aaae0103': row[1],
                        'aaae0113': row[2],
                        'aaae0123': row[3]
                    }
                data_list.append(data)
            except Exception as e:
                print(f"解析数据失败: {e}")
                # 构造基本数据作为备选
                data = {
                    'aaae0102': row[0],
                    'aaae0103': row[1],
                    'aaae0113': row[2],
                    'aaae0123': row[3]
                }
                data_list.append(data)

        conn.close()
        return data_list

    def delete_data(self, keyword, data_list):
        """删除指定的数据记录"""
        if not data_list:
            return 0

        table_name = self.get_table_name(keyword)
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        deleted_count = 0
        for data in data_list:
            try:
                code = data.get('aaae0102', '').strip()
                if code:
                    cursor.execute(f'DELETE FROM {table_name} WHERE aaae0102 = ?', (code,))
                    if cursor.rowcount > 0:
                        deleted_count += 1
            except Exception as e:
                print(f"删除数据失败: {e}")

        conn.commit()
        conn.close()
        return deleted_count
