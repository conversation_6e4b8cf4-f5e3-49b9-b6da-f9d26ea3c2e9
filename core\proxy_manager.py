"""
代理池管理模块
"""
import requests
import random
import time
from urllib.parse import urlparse


class ProxyManager:
    """代理池管理类"""
    
    def __init__(self, config_manager=None):
        self.proxy_list = []
        self.current_proxy = None
        self.failed_proxies = {}  # 改为字典，存储失败时间: {proxy: fail_time}
        self.failure_timeout = 300  # 5分钟后重新尝试失败的代理
        self.proxy_failure_count = {}  # 记录每个代理的连续失败次数: {proxy: count}
        self.config_manager = config_manager
        # 从配置中读取最大连续失败次数，默认为3
        self.max_consecutive_failures = 3
        if self.config_manager:
            self.max_consecutive_failures = self.config_manager.get_proxy_max_consecutive_failures()

        # 快代理管理器
        self.kuaidaili_manager = None
        if self.config_manager:
            from core.kuaidaili_manager import KuaidailiManager
            self.kuaidaili_manager = KuaidailiManager(self.config_manager)
        
    def load_proxies(self, proxy_list=None):
        """加载代理列表（兼容性方法，现在主要使用快代理API）"""
        # 清理状态
        self.failed_proxies.clear()
        self.proxy_failure_count.clear()

        # 如果传入了代理列表，则加载（主要用于快代理获取的代理）
        if proxy_list:
            self.proxy_list = []
            for proxy in proxy_list:
                proxy = proxy.strip()
                if proxy and self._validate_proxy_format(proxy):
                    self.proxy_list.append(proxy)
            print(f"代理管理器加载了 {len(self.proxy_list)} 个代理")
        else:
            # 如果没有传入代理列表，清空现有列表
            self.proxy_list = []
            print("代理管理器已清空代理列表")
                
    def _validate_proxy_format(self, proxy):
        """验证代理格式"""
        try:
            # 支持格式: ip:port 或 http://ip:port
            if '://' not in proxy:
                proxy = 'http://' + proxy
                
            parsed = urlparse(proxy)
            return parsed.hostname and parsed.port
        except:
            return False
            
    def verify_proxy(self, proxy_config, timeout=8):
        """验证代理是否可用 - 使用百度进行测试"""
        if not proxy_config:
            return False

        # 从代理配置中提取IP信息用于显示
        proxy_info = proxy_config.get('http', '').replace('http://', '')

        try:
            # 先进行快速连接测试
            if not self._quick_proxy_test(proxy_config, timeout=5):
                return False

            # 再进行完整的HTTP测试
            return self._full_proxy_test(proxy_config, proxy_info, timeout)

        except Exception as e:
            print(f"❌ 代理验证失败: {str(e)}")
            return False

    def _quick_proxy_test(self, proxy_config, timeout=5):
        """快速代理连接测试"""
        try:
            # 使用HEAD请求进行快速测试
            response = requests.head(
                "http://www.baidu.com",
                proxies=proxy_config,
                timeout=timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            return response.status_code in [200, 301, 302]
        except:
            return False

    def _full_proxy_test(self, proxy_config, proxy_info, timeout=8):
        """完整的代理HTTP测试"""
        try:
            # 使用百度作为测试目标（适合国内代理）
            response = requests.get(
                "http://www.baidu.com",
                proxies=proxy_config,
                timeout=timeout,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                }
            )

            if response.status_code == 200:
                # 检查响应内容是否包含百度特征
                content = response.text.lower()
                if 'baidu' in content or '百度' in content:
                    print(f"✅ 代理验证成功: {proxy_info}")
                    return True
                else:
                    print(f"❌ 代理验证失败: 响应内容异常（可能被劫持）")
                    return False
            else:
                print(f"❌ 代理验证失败: HTTP {response.status_code}")
                return False

        except requests.exceptions.ConnectTimeout:
            print(f"❌ 代理验证失败: 连接超时")
            return False
        except requests.exceptions.ReadTimeout:
            print(f"❌ 代理验证失败: 读取超时")
            return False
        except requests.exceptions.ProxyError as e:
            print(f"❌ 代理验证失败: 代理错误 - {str(e)}")
            return False
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 代理验证失败: 连接错误 - {str(e)}")
            return False
        except Exception as e:
            print(f"❌ 代理验证失败: {str(e)}")
            return False

    def get_verified_proxy(self, max_attempts=5):
        """获取经过验证的可用代理"""
        attempts = 0
        while attempts < max_attempts:
            attempts += 1
            print(f"🔄 获取代理尝试 {attempts}/{max_attempts}")

            # 获取代理配置
            proxy_config = self._get_proxy_without_verification()

            if not proxy_config:
                print(f"❌ 第 {attempts} 次尝试：无法获取代理配置")
                # 如果是快代理API获取失败，等待一段时间再重试
                if (self.config_manager and
                    self.config_manager.is_kuaidaili_enabled() and
                    attempts < max_attempts):
                    import time
                    wait_time = min(attempts * 2, 10)  # 最多等待10秒
                    print(f"⏳ 等待 {wait_time} 秒后重试获取代理配置...")
                    time.sleep(wait_time)
                continue

            # 验证代理是否可用（使用较短的超时时间）
            if self.verify_proxy(proxy_config, timeout=6):
                print(f"✅ 第 {attempts} 次尝试：代理验证通过")
                return proxy_config
            else:
                print(f"❌ 第 {attempts} 次尝试：代理验证失败，重新获取")
                # 标记当前代理失败
                self.mark_proxy_failed()
                # 清除当前代理，强制下次获取新代理
                self.current_proxy = None

                # 如果验证失败，稍微等待一下再重试
                if attempts < max_attempts:
                    import time
                    wait_time = min(attempts, 3)  # 最多等待3秒，加快验证速度
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

        print(f"❌ 尝试了 {max_attempts} 次，未找到可用代理")
        print("💡 建议检查：1) 网络连接 2) 快代理API状态 3) 代理服务器可用性")
        return None

    def get_proxy(self):
        """获取经过验证的可用代理"""
        return self.get_verified_proxy()

    def _get_proxy_without_verification(self):
        """获取代理配置（不进行验证，内部使用）"""
        # 检查是否启用快代理API
        if (self.config_manager and
            self.config_manager.is_kuaidaili_enabled() and
            self.kuaidaili_manager):

            # 使用快代理API获取代理
            return self._get_kuaidaili_proxy()

        # 使用手动配置的代理
        return self._get_manual_proxy()

    def _get_kuaidaili_proxy(self):
        """从快代理API获取代理（每次都获取新代理）"""
        try:
            # 每次都获取新的代理，不使用缓存
            proxy_count = self.config_manager.get_kuaidaili_proxy_count()
            proxies = self.kuaidaili_manager.get_fresh_proxy(proxy_count)

            if not proxies:
                print("❌ 快代理API无法获取代理")
                return None

            # 随机选择一个代理
            import random
            proxy = random.choice(proxies)
            self.current_proxy = proxy

            # 格式化代理
            if '://' not in proxy:
                proxy = 'http://' + proxy

            return {
                'http': proxy,
                'https': proxy
            }

        except Exception as e:
            print(f"❌ 快代理获取失败: {e}")
            return None

    def _get_manual_proxy(self):
        """获取手动配置的代理"""
        if not self.proxy_list:
            return None

        # 清理过期的失败记录
        self._cleanup_expired_failures()

        # 过滤掉失败的代理
        available_proxies = [p for p in self.proxy_list if p not in self.failed_proxies]

        if not available_proxies:
            # 如果所有代理都失败了，重置失败列表
            print("所有代理都失败，重置失败列表重新开始")
            self.failed_proxies.clear()
            available_proxies = self.proxy_list

        # 随机选择一个代理
        proxy = random.choice(available_proxies)
        self.current_proxy = proxy

        # 格式化代理
        if '://' not in proxy:
            proxy = 'http://' + proxy

        return {
            'http': proxy,
            'https': proxy
        }
        
    def mark_proxy_failed(self, proxy=None):
        """标记代理失败（基于连续失败次数）"""
        if proxy is None:
            proxy = self.current_proxy

        if proxy:
            # 增加失败计数
            self.proxy_failure_count[proxy] = self.proxy_failure_count.get(proxy, 0) + 1
            failure_count = self.proxy_failure_count[proxy]

            print(f"代理 {proxy} 失败 {failure_count} 次")

            # 只有连续失败达到阈值才暂时禁用
            if failure_count >= self.max_consecutive_failures:
                import time
                self.failed_proxies[proxy] = time.time()
                print(f"代理 {proxy} 连续失败 {failure_count} 次，暂时禁用 {self.failure_timeout} 秒")
                # 重置失败计数
                self.proxy_failure_count[proxy] = 0
            else:
                print(f"代理 {proxy} 还有 {self.max_consecutive_failures - failure_count} 次机会")

    def mark_proxy_success(self, proxy=None):
        """标记代理成功，重置失败计数"""
        if proxy is None:
            proxy = self.current_proxy

        if proxy and proxy in self.proxy_failure_count:
            old_count = self.proxy_failure_count[proxy]
            self.proxy_failure_count[proxy] = 0
            if old_count > 0:
                print(f"代理 {proxy} 请求成功，重置失败计数（之前失败 {old_count} 次）")

    def update_config(self):
        """更新配置（重新读取最大连续失败次数）"""
        if self.config_manager:
            old_value = self.max_consecutive_failures
            self.max_consecutive_failures = self.config_manager.get_proxy_max_consecutive_failures()
            if old_value != self.max_consecutive_failures:
                print(f"代理最大连续失败次数已更新: {old_value} -> {self.max_consecutive_failures}")

    def _cleanup_expired_failures(self):
        """清理过期的失败记录"""
        import time
        current_time = time.time()
        expired_proxies = []

        for proxy, fail_time in self.failed_proxies.items():
            if current_time - fail_time > self.failure_timeout:
                expired_proxies.append(proxy)

        for proxy in expired_proxies:
            del self.failed_proxies[proxy]
            print(f"代理 {proxy} 失败超时已过，重新加入可用列表")
            
    def test_proxy(self, proxy):
        """测试单个代理"""
        try:
            if '://' not in proxy:
                proxy = 'http://' + proxy
                
            proxies = {
                'http': proxy,
                'https': proxy
            }
            
            # 使用百度作为测试目标
            response = requests.get(
                'http://www.baidu.com',
                proxies=proxies,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception:
            return False
            
    def test_all_proxies(self):
        """测试所有代理"""
        results = {}
        
        for proxy in self.proxy_list:
            results[proxy] = self.test_proxy(proxy)
            time.sleep(1)  # 避免请求过快
            
        return results
        
    def get_proxy_count(self):
        """获取代理总数"""
        return len(self.proxy_list)
        
    def get_available_proxy_count(self):
        """获取可用代理数"""
        self._cleanup_expired_failures()  # 先清理过期记录
        return len([p for p in self.proxy_list if p not in self.failed_proxies])

    def reset_failed_proxies(self):
        """重置失败代理列表和失败计数"""
        failed_count = len(self.failed_proxies)
        failure_count = len(self.proxy_failure_count)
        self.failed_proxies.clear()
        self.proxy_failure_count.clear()
        print(f"手动重置了 {failed_count} 个失败代理记录和 {failure_count} 个失败计数")

    def get_proxy_status(self):
        """获取代理状态信息"""
        self._cleanup_expired_failures()

        total = len(self.proxy_list)
        failed = len(self.failed_proxies)
        available = total - failed

        status = {
            'total': total,
            'available': available,
            'failed': failed,
            'failed_list': list(self.failed_proxies.keys())
        }

        return status

    def get_current_proxy_config(self):
        """获取当前代理的配置字典（不获取新代理）"""
        if not self.current_proxy:
            return None

        proxy = self.current_proxy
        if '://' not in proxy:
            proxy = 'http://' + proxy

        return {
            'http': proxy,
            'https': proxy
        }
