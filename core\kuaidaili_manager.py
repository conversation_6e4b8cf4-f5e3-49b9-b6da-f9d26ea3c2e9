"""
快代理API管理模块
"""
import requests
from datetime import datetime, timedelta


class KuaidailiManager:
    """快代理API管理类"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager
        self.api_url = "https://dps.kdlapi.com/api/getdps"
        self.proxy_cache = []  # 代理缓存
        self.cache_expire_time = None  # 缓存过期时间
        self.cache_duration = 300  # 缓存5分钟
        
    def get_api_credentials(self):
        """获取API凭证"""
        if not self.config_manager:
            return None, None
            
        secret_id = self.config_manager.get_kuaidaili_secret_id()
        signature = self.config_manager.get_kuaidaili_signature()
        
        return secret_id, signature
    
    def fetch_proxies(self, num=10):
        """从快代理API获取代理"""
        secret_id, signature = self.get_api_credentials()
        
        if not secret_id or not signature:
            print("❌ 快代理API凭证未配置")
            return []
        
        params = {
            "secret_id": secret_id,
            "signature": signature,
            "num": num,  # 提取数量
        }
        
        try:
            print(f"🔄 正在从快代理API获取 {num} 个代理...")
            response = requests.get(self.api_url, params=params, timeout=10)
            
            if response.status_code == 200:
                # 快代理返回的是纯文本，每行一个代理
                proxy_text = response.text.strip()
                if proxy_text:
                    # 检查是否是错误信息
                    if "error" in proxy_text.lower() or "fail" in proxy_text.lower():
                        print(f"❌ 快代理API返回错误: {proxy_text}")
                        return []

                    proxies = [line.strip() for line in proxy_text.split('\n') if line.strip()]
                    if proxies:
                        print(f"✅ 成功获取 {len(proxies)} 个代理")
                        return proxies
                    else:
                        print("❌ 快代理API返回内容无法解析为代理列表")
                        return []
                else:
                    print("❌ 快代理API返回空内容，可能原因：")
                    print("   1. API凭证错误")
                    print("   2. 账户余额不足")
                    print("   3. 请求参数错误")
                    return []
            else:
                print(f"❌ 快代理API请求失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                # 根据状态码提供更具体的错误信息
                if response.status_code == 401:
                    print("💡 401错误通常表示API凭证无效，请检查Secret ID和Signature")
                elif response.status_code == 403:
                    print("💡 403错误通常表示账户余额不足或权限不够")
                elif response.status_code == 429:
                    print("💡 429错误表示请求过于频繁，请稍后再试")
                return []
                
        except requests.RequestException as e:
            print(f"❌ 快代理API请求异常: {e}")
            return []
        except Exception as e:
            print(f"❌ 获取快代理时发生错误: {e}")
            return []
    
    def get_cached_proxies(self, num=10, force_refresh=False):
        """获取缓存的代理（如果缓存过期则重新获取）"""
        current_time = datetime.now()

        # 检查是否需要刷新缓存
        if (force_refresh or
            not self.proxy_cache or
            not self.cache_expire_time or
            current_time > self.cache_expire_time):

            print("🔄 代理缓存已过期或为空，重新获取...")
            new_proxies = self.fetch_proxies(num)

            if new_proxies:
                self.proxy_cache = new_proxies
                self.cache_expire_time = current_time + timedelta(seconds=self.cache_duration)
                print(f"✅ 代理缓存已更新，缓存 {len(self.proxy_cache)} 个代理")
            else:
                print("❌ 无法获取新代理，使用现有缓存")

        return self.proxy_cache.copy()

    def get_fresh_proxy(self, num=1, max_retries=5):
        """每次都获取新的代理（不使用缓存，支持重试）"""
        for retry in range(max_retries):
            if retry > 0:
                print(f"🔄 快代理API重试 {retry + 1}/{max_retries}")
                # 递增等待时间：2秒、4秒、6秒、8秒、10秒
                import time
                wait_time = retry * 2
                if wait_time > 0:
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
            else:
                print(f"🔄 获取新的代理IP (需要 {num} 个)...")

            new_proxies = self.fetch_proxies(num)
            if new_proxies:
                print(f"✅ 成功获取 {len(new_proxies)} 个新代理")
                return new_proxies
            else:
                print(f"❌ 第 {retry + 1} 次获取新代理失败")
                # 分析失败原因
                if retry == 0:
                    print("💡 可能的原因：1) 网络连接问题 2) API凭证错误 3) 账户余额不足")

        print(f"❌ 快代理API重试 {max_retries} 次后仍然失败")
        print("🔄 尝试使用缓存代理作为备选...")
        cached_proxies = self.get_cached_proxies(num)
        if cached_proxies:
            print(f"✅ 使用缓存代理: {len(cached_proxies)} 个")
            return cached_proxies
        else:
            print("❌ 缓存代理也不可用")
            return []
    
    def test_api_connection(self):
        """测试快代理API连接"""
        secret_id, signature = self.get_api_credentials()

        if not secret_id or not signature:
            return False, "API凭证未配置"

        params = {
            "secret_id": secret_id,
            "signature": signature,
            "num": 1,  # 获取1个代理进行测试
        }

        try:
            response = requests.get(self.api_url, params=params, timeout=10)

            if response.status_code == 200:
                proxy_text = response.text.strip()
                if proxy_text and ':' in proxy_text:
                    # 不显示具体的代理IP，只确认连接成功
                    return True, "API连接正常"
                else:
                    return False, f"API响应异常: {proxy_text}"
            else:
                return False, f"HTTP错误: {response.status_code} - {response.text}"

        except requests.RequestException as e:
            return False, f"网络请求失败: {e}"
        except Exception as e:
            return False, f"未知错误: {e}"
    
    def get_proxy_info(self):
        """获取代理信息统计"""
        info = {
            'cache_count': len(self.proxy_cache),
            'cache_expire_time': self.cache_expire_time,
            'is_expired': (
                not self.cache_expire_time or 
                datetime.now() > self.cache_expire_time
            ),
            'api_configured': bool(self.get_api_credentials()[0] and self.get_api_credentials()[1])
        }
        return info
    
    def clear_cache(self):
        """清空代理缓存"""
        self.proxy_cache.clear()
        self.cache_expire_time = None
        print("🗑️ 代理缓存已清空")
    
    def set_cache_duration(self, seconds):
        """设置缓存持续时间"""
        self.cache_duration = seconds
        print(f"⏰ 代理缓存时间已设置为 {seconds} 秒")
