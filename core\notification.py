"""
企业微信通知模块
"""
import requests
import json
import time


class NotificationManager:
    """通知管理类"""
    
    def __init__(self, webhook_url=""):
        self.webhook_url = webhook_url
        
    def set_webhook_url(self, url):
        """设置webhook地址"""
        self.webhook_url = url
        
    def send_notification(self, keyword, new_data_list):
        """发送新增数据通知"""
        if not self.webhook_url:
            return False, "未配置webhook地址"
            
        try:
            # 构建消息内容
            message = self._build_message(keyword, new_data_list)
            
            # 发送请求
            response = requests.post(
                self.webhook_url,
                json=message,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    return True, "发送成功"
                else:
                    return False, f"发送失败: {result.get('errmsg', '未知错误')}"
            else:
                return False, f"HTTP错误: {response.status_code}"
                
        except Exception as e:
            return False, f"发送异常: {str(e)}"
            
    def _build_message(self, keyword, new_data_list):
        """构建消息内容"""
        # 美化的消息标题
        title = f"🎉 【新增数据提醒】 🎉"
        subtitle = f"📚 {keyword} 发现 {len(new_data_list)} 条新增记录"

        # 构建数据内容
        content_lines = [
            title,
            subtitle,
            ""
        ]

        for i, data in enumerate(new_data_list, 1):
            org_name = data.get('aaae0103', '').strip()
            org_code = data.get('aaae0102', '').strip()
            person = data.get('aaae0113', '').strip()
            valid_date = data.get('aaae0123', '').strip()

            # 限制组织名称长度
            if len(org_name) > 35:
                org_name = org_name[:32] + "..."

            content_lines.append(f"📋 【第 {i} 条】")
            content_lines.append(f"🏫 机构名称: {org_name}")
            content_lines.append(f"🆔 统一代码: {org_code}")
            content_lines.append(f"👤 负责人员: {person}")
            content_lines.append(f"📅 成立时间: {valid_date}")

            # 如果不是最后一条，添加分隔线
            if i < len(new_data_list):
                content_lines.append("┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈")
            content_lines.append("")

        # 底部信息
        content_lines.extend([
            f"📊 统计信息: 共发现 {len(new_data_list)} 条新增记录",
            f"⏰ 检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
        ])

        # 构建企业微信消息格式
        message = {
            "msgtype": "text",
            "text": {
                "content": "\n".join(content_lines)
            }
        }

        return message
        
    def test_webhook(self):
        """测试webhook连接"""
        if not self.webhook_url:
            return False, "未配置webhook地址"
            
        try:
            test_message = {
                "msgtype": "text",
                "text": {
                    "content": "🧪 【连接测试】 🧪\n\n🎯 关键词监控工具\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n✅ 如果您收到此消息，说明webhook配置正确！\n\n🤖 系统已准备就绪，开始监控新增数据\n⏰ 测试时间: " + time.strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
            response = requests.post(
                self.webhook_url,
                json=test_message,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    return True, "测试成功"
                else:
                    return False, f"测试失败: {result.get('errmsg', '未知错误')}"
            else:
                return False, f"HTTP错误: {response.status_code}"
                
        except Exception as e:
            return False, f"测试异常: {str(e)}"
