"""
模拟真实的数据获取过程，分析"常州市长荡湖高级中学"重复通知的根本原因
"""
import sys
import os
import json
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.database import DatabaseManager
from core.crawler import CrawlerCore
from core.monitor import MonitorCore
from core.proxy_manager import ProxyManager
from utils.resource_manager import ResourceManager
from utils.config import ConfigManager

class RealDataFetchSimulator:
    """真实数据获取模拟器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.config_manager = ConfigManager()
        self.resource_manager = ResourceManager()
        self.proxy_manager = ProxyManager(self.config_manager)
        self.crawler = CrawlerCore(self.resource_manager, self.proxy_manager, self.config_manager)
        self.target_school = "常州市长荡湖高级中学"
        self.target_keyword = "高级中学"
        
    def test_real_data_fetch(self):
        """测试真实的数据获取"""
        print("=" * 60)
        print("🌐 测试真实数据获取")
        print("=" * 60)
        
        try:
            print("🔄 正在获取网站数据...")
            
            # 尝试获取小量数据进行测试
            result = self.crawler.try_with_retry(self.target_keyword, page_size=50, max_retries=3, max_proxy_cycles=1)
            
            if result[0] is None:
                print("❌ 无法获取网站数据，可能的原因:")
                print("   - 网络连接问题")
                print("   - 代理配置问题") 
                print("   - 验证码识别失败")
                print("   - 网站反爬虫机制")
                return None
            
            data, success_attempt, total_count = result
            print(f"✅ 数据获取成功！")
            print(f"📊 获取到 {len(data)} 条记录，网站总数: {total_count}")
            
            # 查找目标学校
            target_found = False
            target_record = None
            
            for record in data:
                if self.target_school in record.get('aaae0103', ''):
                    target_found = True
                    target_record = record
                    break
            
            if target_found:
                print(f"🎯 在网站数据中找到目标学校:")
                print(f"   组织代码: {target_record.get('aaae0102', 'N/A')}")
                print(f"   组织名称: {target_record.get('aaae0103', 'N/A')}")
                print(f"   aaae0113: {target_record.get('aaae0113', 'N/A')}")
                print(f"   aaae0123: {target_record.get('aaae0123', 'N/A')}")
            else:
                print(f"❌ 在当前获取的数据中未找到目标学校")
                print("   可能原因:")
                print("   - 学校不在前50条记录中")
                print("   - 学校名称发生了变化")
                print("   - 数据获取不完整")
            
            return data, target_record
            
        except Exception as e:
            print(f"❌ 数据获取过程中发生错误: {str(e)}")
            return None
    
    def analyze_data_consistency(self, website_data):
        """分析数据一致性"""
        print("=" * 60)
        print("🔍 分析数据一致性")
        print("=" * 60)
        
        if not website_data:
            print("❌ 没有网站数据可供分析")
            return
        
        # 获取数据库数据
        db_data = self.db_manager.get_all_data(self.target_keyword)
        
        print(f"📊 数据对比:")
        print(f"   网站数据: {len(website_data)} 条")
        print(f"   数据库数据: {len(db_data)} 条")
        
        # 分析组织代码的重叠情况
        website_codes = set()
        for item in website_data:
            code = item.get('aaae0102', '').strip()
            if code:
                website_codes.add(code)
        
        db_codes = set()
        for item in db_data:
            code = item.get('aaae0102', '').strip()
            if code:
                db_codes.add(code)
        
        common_codes = website_codes & db_codes
        website_only = website_codes - db_codes
        db_only = db_codes - website_codes
        
        print(f"\n🔄 代码重叠分析:")
        print(f"   共同代码: {len(common_codes)} 个")
        print(f"   仅在网站: {len(website_only)} 个")
        print(f"   仅在数据库: {len(db_only)} 个")
        
        if website_only:
            print(f"\n📋 仅在网站中的代码 (前10个):")
            for i, code in enumerate(list(website_only)[:10]):
                # 找到对应的记录
                for item in website_data:
                    if item.get('aaae0102', '').strip() == code:
                        print(f"   {i+1}. {code} - {item.get('aaae0103', 'N/A')}")
                        break
        
        return {
            'website_codes': website_codes,
            'db_codes': db_codes,
            'common_codes': common_codes,
            'website_only': website_only,
            'db_only': db_only
        }
    
    def simulate_monitoring_cycle(self):
        """模拟完整的监控周期"""
        print("=" * 60)
        print("🔄 模拟完整监控周期")
        print("=" * 60)
        
        # 记录开始状态
        initial_count = self.db_manager.get_record_count(self.target_keyword)
        print(f"📊 监控开始时数据库记录数: {initial_count}")
        
        # 模拟监控核心的检查过程
        monitor = MonitorCore()
        monitor.config_manager = self.config_manager
        monitor.db_manager = self.db_manager
        monitor.crawler = self.crawler
        
        print("\n🔄 开始模拟检查过程...")
        
        try:
            # 获取数据库中的实际记录数量
            last_count = self.db_manager.get_record_count(self.target_keyword)
            print(f"💾 数据库中现有 {self.target_keyword} 记录: {last_count} 条")

            # 先用小页面大小检查总数
            result = self.crawler.try_with_retry(self.target_keyword, page_size=10, max_retries=3, max_proxy_cycles=1)

            if result[0] is None:
                print(f"❌ 获取 {self.target_keyword} 数据失败")
                return

            data, success_attempt, total_count = result
            print(f"✅ 数据获取成功！网站总数: {total_count}")

            print(f"📊 对比结果: 网站总数 {total_count} vs 数据库记录 {last_count}")

            # 检查数据是否有变化
            if total_count != last_count:
                print(f"⚠️ 数据数量不一致，需要详细对比")
                
                # 用大页面大小重新获取数据
                result = self.crawler.try_with_retry(self.target_keyword, page_size=200, max_retries=3, max_proxy_cycles=1)
                if result[0] is not None:
                    data, _, _ = result
                    print(f"✅ 完整数据获取成功，共 {len(data)} 条记录")

                    # 进行完整的数据同步对比
                    sync_result = monitor._sync_database_with_website(self.target_keyword, data)

                    new_data = sync_result['new_data']
                    deleted_data = sync_result['deleted_data']
                    
                    print(f"\n🔄 同步结果:")
                    print(f"   新增数据: {len(new_data)} 条")
                    print(f"   删除数据: {len(deleted_data)} 条")
                    
                    # 检查目标学校是否在新增数据中
                    target_in_new = False
                    for item in new_data:
                        if self.target_school in item.get('aaae0103', ''):
                            target_in_new = True
                            print(f"⚠️ 发现目标学校在新增数据中:")
                            print(f"   {item}")
                            break
                    
                    if not target_in_new:
                        print(f"✅ 目标学校未在新增数据中")
                    
                    return {
                        'new_data': new_data,
                        'deleted_data': deleted_data,
                        'target_in_new': target_in_new
                    }
            else:
                print(f"✅ {self.target_keyword} 数据数量一致，无需详细对比")
                return {'new_data': [], 'deleted_data': [], 'target_in_new': False}
                
        except Exception as e:
            print(f"❌ 监控过程中发生错误: {str(e)}")
            return None
    
    def test_multiple_fetch_consistency(self):
        """测试多次获取的数据一致性"""
        print("=" * 60)
        print("🔄 测试多次获取的数据一致性")
        print("=" * 60)
        
        fetch_results = []
        target_school_results = []
        
        for i in range(3):
            print(f"\n--- 第 {i+1} 次获取 ---")
            try:
                result = self.crawler.try_with_retry(self.target_keyword, page_size=50, max_retries=2, max_proxy_cycles=1)
                
                if result[0] is not None:
                    data, _, total_count = result
                    fetch_results.append({
                        'attempt': i+1,
                        'data_count': len(data),
                        'total_count': total_count,
                        'success': True
                    })
                    
                    # 检查目标学校
                    target_found = False
                    for record in data:
                        if self.target_school in record.get('aaae0103', ''):
                            target_found = True
                            target_school_results.append({
                                'attempt': i+1,
                                'found': True,
                                'record': record
                            })
                            break
                    
                    if not target_found:
                        target_school_results.append({
                            'attempt': i+1,
                            'found': False,
                            'record': None
                        })
                    
                    print(f"✅ 获取成功: {len(data)} 条记录，总数: {total_count}")
                    print(f"🎯 目标学校: {'找到' if target_found else '未找到'}")
                else:
                    fetch_results.append({
                        'attempt': i+1,
                        'success': False
                    })
                    print(f"❌ 获取失败")
                
                # 间隔一段时间
                if i < 2:
                    print("⏰ 等待5秒...")
                    time.sleep(5)
                    
            except Exception as e:
                print(f"❌ 第 {i+1} 次获取出错: {str(e)}")
        
        # 分析结果
        print(f"\n📊 多次获取结果分析:")
        successful_fetches = [r for r in fetch_results if r.get('success', False)]
        print(f"   成功获取次数: {len(successful_fetches)}/3")
        
        if successful_fetches:
            total_counts = [r['total_count'] for r in successful_fetches]
            data_counts = [r['data_count'] for r in successful_fetches]
            
            print(f"   总数变化: {total_counts}")
            print(f"   获取数量变化: {data_counts}")
            
            # 检查总数是否一致
            if len(set(total_counts)) == 1:
                print("   ✅ 网站总数保持一致")
            else:
                print("   ⚠️ 网站总数发生变化！这可能是重复通知的原因")
        
        # 分析目标学校的出现情况
        target_found_count = sum(1 for r in target_school_results if r['found'])
        print(f"\n🎯 目标学校出现情况:")
        print(f"   出现次数: {target_found_count}/{len(target_school_results)}")
        
        if target_found_count > 0:
            print("   详细记录:")
            for r in target_school_results:
                if r['found']:
                    record = r['record']
                    print(f"   第{r['attempt']}次: {record.get('aaae0102', 'N/A')} - {record.get('aaae0103', 'N/A')}")
        
        return fetch_results, target_school_results
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🚀 开始综合分析'常州市长荡湖高级中学'重复通知问题")
        print("时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        print()
        
        # 1. 测试真实数据获取
        fetch_result = self.test_real_data_fetch()
        
        if fetch_result:
            website_data, target_record = fetch_result
            
            # 2. 分析数据一致性
            consistency_result = self.analyze_data_consistency(website_data)
            
            # 3. 模拟监控周期
            monitoring_result = self.simulate_monitoring_cycle()
        
        # 4. 测试多次获取的一致性
        fetch_results, target_results = self.test_multiple_fetch_consistency()
        
        # 5. 总结分析
        print("=" * 60)
        print("📋 综合分析总结")
        print("=" * 60)
        
        print("🔍 可能的重复通知原因:")
        print("1. 网站数据的动态变化")
        print("2. 数据获取过程中的不一致性")
        print("3. 代理或网络问题导致的数据差异")
        print("4. 验证码识别失败导致的重试")
        print("5. 数据同步逻辑中的边界条件")

if __name__ == "__main__":
    simulator = RealDataFetchSimulator()
    simulator.run_comprehensive_analysis()
