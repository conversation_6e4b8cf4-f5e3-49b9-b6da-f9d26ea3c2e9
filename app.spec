# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

# 数据文件列表
datas = [
    # JavaScript文件
    (os.path.join(current_dir, 'main.js'), '.'),
    (os.path.join(current_dir, 'package.json'), '.'),
    
    # 图标文件
    (os.path.join(current_dir, 'logo.ico'), '.'),
    
    # node_modules目录
    (os.path.join(current_dir, 'node_modules'), 'node_modules'),
    
    # 资源目录（如果存在）
]

# 检查resources目录
resources_dir = os.path.join(current_dir, 'resources')
if os.path.exists(resources_dir):
    datas.append((resources_dir, 'resources'))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'sqlite3',
    'requests',
    'execjs',
    'ddddocr',
    'numpy',
    'PIL',
    'cryptography',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'IPython',
]

a = Analysis(
    [os.path.join(current_dir, 'app_main.py')],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='中国社会组织监控工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(current_dir, 'logo.ico'),
)
