{"name": "get-random-values", "version": "1.2.2", "description": "`window.crypto.getRandomValues` with fallback to Node.js crypto", "keywords": ["crypto"], "repository": "KenanY/get-random-values", "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>> (http://kenany.me/)", "main": "index.js", "files": ["index.js", "LICENSE.txt"], "directories": {"test": "test"}, "engines": {"node": "10 || 12 || >=14"}, "scripts": {"release": "semantic-release", "test": "tape test/*.js"}, "dependencies": {"global": "^4.4.0"}, "devDependencies": {"@kenan/renovate-config": "1.4.0", "@semantic-release/changelog": "5.0.1", "@semantic-release/git": "9.0.0", "conventional-changelog-conventionalcommits": "4.4.0", "is-browser": "2.1.0", "lodash.foreach": "4.5.0", "lodash.isfunction": "3.0.9", "semantic-release": "17.1.1", "tape": "5.0.1"}, "browser": {"crypto": false}}