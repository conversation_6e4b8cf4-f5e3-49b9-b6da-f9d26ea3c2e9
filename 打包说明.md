# 中国社会组织监控工具 - Nuitka打包说明

## 📋 打包前准备

### 1. 环境要求

- **Python 3.8+**: 确保Python已安装并添加到PATH
- **Node.js 16+**: 用于JavaScript依赖管理
- **npm**: Node.js包管理器
- **Git**: 用于下载依赖（可选）

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Node.js依赖
npm install
```

### 3. 检查必要文件

确保以下文件存在：
- ✅ `app_main.py` - 主程序入口
- ✅ `main.js` - JavaScript加密模块
- ✅ `package.json` - Node.js依赖配置
- ✅ `node_modules/` - Node.js依赖目录
- ✅ `logo.ico` - 程序图标
- ✅ `icon.ico` - 备用图标
- ✅ `core/`, `ui/`, `utils/` - Python模块目录

## 🚀 打包方法

### 方法一：使用批处理文件（推荐）

```bash
# Windows用户直接双击运行
build.bat
```

### 方法二：使用Python脚本

```bash
# 完整版打包脚本
python build_nuitka.py

# 简化版打包脚本
python build_simple.py
```

### 方法三：手动执行Nuitka命令

```bash
nuitka \
    --standalone \
    --onefile \
    --windows-disable-console \
    --enable-plugin=pyqt5 \
    --windows-icon-from-ico=logo.ico \
    --output-filename=中国社会组织监控工具.exe \
    --include-data-file=main.js=main.js \
    --include-data-file=package.json=package.json \
    --include-data-dir=node_modules=node_modules \
    --include-data-file=logo.ico=logo.ico \
    --include-data-file=icon.ico=icon.ico \
    --include-package-data=core \
    --include-package-data=ui \
    --include-package-data=utils \
    --assume-yes-for-downloads \
    --show-progress \
    app_main.py
```

## 📦 打包参数说明

| 参数 | 说明 |
|------|------|
| `--standalone` | 创建独立的可执行文件 |
| `--onefile` | 打包为单个文件 |
| `--windows-disable-console` | Windows下隐藏控制台窗口 |
| `--enable-plugin=pyqt5` | 启用PyQt5插件支持 |
| `--windows-icon-from-ico` | 设置Windows图标 |
| `--include-data-file` | 包含单个数据文件 |
| `--include-data-dir` | 包含整个目录 |
| `--include-package-data` | 包含Python包数据 |

## 🔧 关键配置

### JavaScript依赖打包

```bash
# 包含main.js文件
--include-data-file=main.js=main.js

# 包含package.json配置
--include-data-file=package.json=package.json

# 包含整个node_modules目录
--include-data-dir=node_modules=node_modules
```

### 图标和资源

```bash
# 程序图标
--windows-icon-from-ico=logo.ico
--include-data-file=logo.ico=logo.ico
--include-data-file=icon.ico=icon.ico
```

### Python模块

```bash
# 包含所有Python模块
--include-package-data=core
--include-package-data=ui
--include-package-data=utils
```

## ⚠️ 常见问题

### 1. Nuitka未安装

```bash
pip install nuitka
```

### 2. Node.js依赖缺失

```bash
npm install
```

### 3. 打包文件过大

- node_modules目录较大是正常的
- 可以考虑使用`--include-data-file`只包含必要的js文件
- 但为了兼容性，建议包含完整的node_modules

### 4. 运行时找不到JavaScript文件

确保使用了正确的包含参数：
```bash
--include-data-file=main.js=main.js
--include-data-dir=node_modules=node_modules
```

## 📊 预期结果

打包完成后会生成：
- `中国社会组织监控工具.exe` - 主可执行文件（约50-100MB）
- 包含所有依赖，可在任何Windows系统上运行
- 无需安装Python、Node.js等环境

## 🎯 测试建议

1. **本机测试**: 在开发机器上运行生成的exe文件
2. **干净环境测试**: 在没有Python/Node.js的机器上测试
3. **功能测试**: 确保所有功能正常工作
4. **性能测试**: 检查启动速度和运行性能

## 📝 注意事项

1. **文件大小**: 由于包含node_modules，文件会比较大
2. **启动速度**: 首次启动可能较慢，这是正常的
3. **杀毒软件**: 某些杀毒软件可能误报，需要添加白名单
4. **系统兼容**: 建议在Windows 10+系统上运行
