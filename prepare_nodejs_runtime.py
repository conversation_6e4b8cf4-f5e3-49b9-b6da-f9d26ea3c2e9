#!/usr/bin/env python3
"""
准备Node.js运行时环境
"""

import os
import sys
import requests
import zipfile
import shutil
from pathlib import Path

def download_nodejs():
    """下载Node.js运行时"""
    print("🔄 准备Node.js运行时环境...")
    
    # Node.js版本和下载URL
    nodejs_version = "v20.11.0"
    nodejs_url = f"https://nodejs.org/dist/{nodejs_version}/node-{nodejs_version}-win-x64.zip"
    
    # 创建resources目录
    resources_dir = Path("resources")
    resources_dir.mkdir(exist_ok=True)
    
    nodejs_dir = resources_dir / "nodejs"
    
    # 如果已经存在，跳过下载
    if nodejs_dir.exists() and (nodejs_dir / "node.exe").exists():
        print("✅ Node.js运行时已存在，跳过下载")
        return True
    
    print(f"📥 下载Node.js {nodejs_version}...")
    
    try:
        # 下载Node.js
        response = requests.get(nodejs_url, stream=True)
        response.raise_for_status()
        
        zip_path = resources_dir / f"node-{nodejs_version}-win-x64.zip"
        
        # 保存zip文件
        with open(zip_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print("📦 解压Node.js...")
        
        # 解压zip文件
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(resources_dir)
        
        # 重命名目录
        extracted_dir = resources_dir / f"node-{nodejs_version}-win-x64"
        if extracted_dir.exists():
            if nodejs_dir.exists():
                shutil.rmtree(nodejs_dir)
            extracted_dir.rename(nodejs_dir)
        
        # 删除zip文件
        zip_path.unlink()
        
        print("✅ Node.js运行时准备完成")
        return True
        
    except Exception as e:
        print(f"❌ 下载Node.js失败: {e}")
        return False

def verify_nodejs():
    """验证Node.js运行时"""
    print("🔍 验证Node.js运行时...")
    
    nodejs_dir = Path("resources/nodejs")
    node_exe = nodejs_dir / "node.exe"
    
    if not node_exe.exists():
        print("❌ Node.js可执行文件不存在")
        return False
    
    try:
        import subprocess
        result = subprocess.run([str(node_exe), "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js验证成功: {version}")
            return True
        else:
            print(f"❌ Node.js验证失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Node.js验证出错: {e}")
        return False

def create_runtime_structure():
    """创建完整的运行时结构"""
    print("🏗️ 创建运行时结构...")
    
    resources_dir = Path("resources")
    resources_dir.mkdir(exist_ok=True)
    
    # 检查必要文件
    required_files = [
        "main.js",
        "package.json",
        "node_modules"
    ]
    
    missing_files = []
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 运行时结构完整")
    return True

def main():
    """主函数"""
    print("🚀 Node.js运行时准备工具")
    print("=" * 50)
    
    # 检查运行时结构
    if not create_runtime_structure():
        return False
    
    # 下载Node.js
    if not download_nodejs():
        return False
    
    # 验证Node.js
    if not verify_nodejs():
        return False
    
    print()
    print("🎉 Node.js运行时准备完成！")
    print()
    print("📋 准备的文件:")
    print("✅ resources/nodejs/node.exe - Node.js可执行文件")
    print("✅ main.js - JavaScript脚本")
    print("✅ package.json - 包配置")
    print("✅ node_modules/ - Node.js依赖")
    print()
    print("现在可以重新打包程序了！")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
