"""
分析"常州市长荡湖高级中学"重复通知问题的专门测试程序
"""
import sys
import os
import sqlite3
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.database import DatabaseManager
from core.crawler import CrawlerCore
from core.monitor import MonitorCore
from utils.resource_manager import ResourceManager
from utils.config import ConfigManager

class DuplicateNotificationAnalyzer:
    """重复通知问题分析器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.config_manager = ConfigManager()
        self.resource_manager = ResourceManager()
        self.target_school = "常州市长荡湖高级中学"
        self.target_keyword = "高级中学"
        
    def analyze_database_records(self):
        """分析数据库中的记录"""
        print("=" * 60)
        print("🔍 分析数据库中的记录")
        print("=" * 60)
        
        # 获取所有高级中学数据
        all_data = self.db_manager.get_all_data(self.target_keyword)
        print(f"📊 数据库中总共有 {len(all_data)} 条高级中学记录")
        
        # 查找目标学校
        target_records = []
        for record in all_data:
            if self.target_school in record.get('aaae0103', ''):
                target_records.append(record)
        
        print(f"🎯 找到 {len(target_records)} 条关于'{self.target_school}'的记录")
        
        if target_records:
            print("\n📋 详细记录信息:")
            for i, record in enumerate(target_records):
                print(f"  记录 {i+1}:")
                print(f"    组织代码 (aaae0102): {record.get('aaae0102', 'N/A')}")
                print(f"    组织名称 (aaae0103): {record.get('aaae0103', 'N/A')}")
                print(f"    aaae0113: {record.get('aaae0113', 'N/A')}")
                print(f"    aaae0123: {record.get('aaae0123', 'N/A')}")
                print(f"    原始数据长度: {len(str(record))}")
                print()
        
        return target_records
    
    def check_database_integrity(self):
        """检查数据库完整性"""
        print("=" * 60)
        print("🔧 检查数据库完整性")
        print("=" * 60)
        
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(gaoji_zhongxue)")
        columns = cursor.fetchall()
        print("📋 表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'UNIQUE' if col[5] else ''}")
        
        # 检查索引
        cursor.execute("PRAGMA index_list(gaoji_zhongxue)")
        indexes = cursor.fetchall()
        print(f"\n📊 索引数量: {len(indexes)}")
        for idx in indexes:
            print(f"  索引: {idx[1]} - {'UNIQUE' if idx[2] else 'NON-UNIQUE'}")
        
        # 检查重复的aaae0102
        cursor.execute("""
            SELECT aaae0102, COUNT(*) as count 
            FROM gaoji_zhongxue 
            GROUP BY aaae0102 
            HAVING COUNT(*) > 1
        """)
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"\n⚠️ 发现 {len(duplicates)} 个重复的组织代码:")
            for dup in duplicates:
                print(f"  代码: {dup[0]} - 重复次数: {dup[1]}")
        else:
            print("\n✅ 没有发现重复的组织代码")
        
        # 检查目标学校的具体记录
        cursor.execute("""
            SELECT id, aaae0102, aaae0103, created_time, updated_time 
            FROM gaoji_zhongxue 
            WHERE aaae0103 LIKE ?
        """, (f'%{self.target_school}%',))
        
        target_db_records = cursor.fetchall()
        print(f"\n🎯 数据库中'{self.target_school}'的记录:")
        for record in target_db_records:
            print(f"  ID: {record[0]}, 代码: {record[1]}, 名称: {record[2]}")
            print(f"  创建时间: {record[3]}, 更新时间: {record[4]}")
        
        conn.close()
        return duplicates, target_db_records
    
    def simulate_data_sync_process(self):
        """模拟数据同步过程"""
        print("=" * 60)
        print("🔄 模拟数据同步过程")
        print("=" * 60)
        
        # 模拟从网站获取的数据（包含目标学校）
        mock_website_data = [
            {
                'aaae0102': 'TEST001',
                'aaae0103': '测试学校1',
                'aaae0113': 'test1',
                'aaae0123': 'test1'
            },
            {
                'aaae0102': 'TEST002', 
                'aaae0103': self.target_school,
                'aaae0113': 'test2',
                'aaae0123': 'test2'
            },
            {
                'aaae0102': 'TEST003',
                'aaae0103': '测试学校3',
                'aaae0113': 'test3', 
                'aaae0123': 'test3'
            }
        ]
        
        print(f"📥 模拟网站数据 ({len(mock_website_data)} 条):")
        for i, data in enumerate(mock_website_data):
            print(f"  {i+1}. {data['aaae0103']} ({data['aaae0102']})")
        
        # 获取数据库现有数据
        db_data = self.db_manager.get_all_data(self.target_keyword)
        print(f"\n💾 数据库现有数据: {len(db_data)} 条")
        
        # 模拟同步过程
        monitor = MonitorCore()
        sync_result = monitor._sync_database_with_website(self.target_keyword, mock_website_data)
        
        print(f"\n🔄 同步结果:")
        print(f"  新增数据: {len(sync_result['new_data'])} 条")
        print(f"  删除数据: {len(sync_result['deleted_data'])} 条")
        
        # 检查目标学校是否在新增数据中
        target_in_new = any(self.target_school in item.get('aaae0103', '') 
                           for item in sync_result['new_data'])
        
        if target_in_new:
            print(f"⚠️ '{self.target_school}' 被识别为新增数据！")
            for item in sync_result['new_data']:
                if self.target_school in item.get('aaae0103', ''):
                    print(f"  新增记录: {item}")
        else:
            print(f"✅ '{self.target_school}' 未被识别为新增数据")
        
        return sync_result
    
    def test_data_comparison_logic(self):
        """测试数据对比逻辑"""
        print("=" * 60)
        print("🧪 测试数据对比逻辑")
        print("=" * 60)
        
        # 获取数据库中已有的组织代码
        existing_codes = self.db_manager.get_existing_codes(self.target_keyword)
        print(f"📊 数据库中已有组织代码数量: {len(existing_codes)}")
        
        # 检查目标学校的组织代码是否在数据库中
        target_records = self.analyze_database_records()
        
        if target_records:
            target_code = target_records[0].get('aaae0102', '')
            print(f"\n🎯 目标学校组织代码: {target_code}")
            print(f"🔍 该代码是否在数据库中: {target_code in existing_codes}")
            
            # 模拟新数据检测
            mock_new_data = [
                {
                    'aaae0102': target_code,
                    'aaae0103': self.target_school,
                    'aaae0113': 'test',
                    'aaae0123': 'test'
                }
            ]
            
            monitor = MonitorCore()
            detected_new = monitor._find_new_data(self.target_keyword, mock_new_data)
            
            print(f"\n🧪 模拟检测结果:")
            print(f"  输入数据: 1 条 (包含目标学校)")
            print(f"  检测到新增: {len(detected_new)} 条")
            
            if detected_new:
                print("⚠️ 目标学校被错误识别为新增数据！")
                for item in detected_new:
                    print(f"  错误新增: {item}")
            else:
                print("✅ 目标学校未被识别为新增数据")
        
        return existing_codes
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 开始分析'常州市长荡湖高级中学'重复通知问题")
        print("时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        print()
        
        # 1. 分析数据库记录
        target_records = self.analyze_database_records()
        
        # 2. 检查数据库完整性
        duplicates, db_records = self.check_database_integrity()
        
        # 3. 测试数据对比逻辑
        existing_codes = self.test_data_comparison_logic()
        
        # 4. 模拟数据同步过程
        sync_result = self.simulate_data_sync_process()
        
        # 5. 总结分析结果
        print("=" * 60)
        print("📋 分析总结")
        print("=" * 60)
        
        print(f"1. 数据库记录状态:")
        print(f"   - 目标学校记录数: {len(target_records)}")
        print(f"   - 数据库重复代码: {len(duplicates)}")
        print(f"   - 总记录数: {len(existing_codes)}")
        
        print(f"\n2. 可能的问题原因:")
        if duplicates:
            print("   ⚠️ 数据库中存在重复的组织代码")
        if len(target_records) > 1:
            print("   ⚠️ 目标学校在数据库中有多条记录")
        if len(target_records) == 0:
            print("   ⚠️ 目标学校在数据库中没有记录")
        
        print(f"\n3. 建议的解决方案:")
        print("   - 检查数据获取过程中的数据一致性")
        print("   - 验证组织代码的唯一性约束")
        print("   - 检查数据同步逻辑中的去重机制")
        print("   - 分析网站数据的变化模式")

if __name__ == "__main__":
    analyzer = DuplicateNotificationAnalyzer()
    analyzer.run_full_analysis()
