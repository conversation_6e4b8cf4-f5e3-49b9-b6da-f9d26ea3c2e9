#!/usr/bin/env python3
"""
中国社会组织监控工具 - PyInstaller打包脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, check=True)
        print(f"✅ Node.js版本: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到Node.js，请先安装Node.js")
        return False
    
    # 检查npm（可选，因为我们已经有node_modules）
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, check=True)
        print(f"✅ npm版本: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ 未找到npm，但已有node_modules目录，继续打包")
    
    return True

def check_files():
    """检查必要文件"""
    print("📁 检查必要文件...")
    
    required_files = [
        'app_main.py',
        'main.js',
        'package.json',
        'logo.ico'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ 找到文件: {file}")
        else:
            print(f"❌ 缺少文件: {file}")
            return False
    
    # 检查node_modules
    if os.path.exists('node_modules') and os.path.isdir('node_modules'):
        print("✅ 找到 node_modules 目录")
        # 显示node_modules大小
        total_size = sum(os.path.getsize(os.path.join(dirpath, filename))
                        for dirpath, _, filenames in os.walk('node_modules')
                        for filename in filenames)
        print(f"   node_modules 大小: {total_size / (1024*1024):.1f} MB")
    else:
        print("❌ 未找到 node_modules 目录")
        return False
    
    return True

def create_spec_file():
    """创建PyInstaller spec文件"""
    print("📝 创建PyInstaller spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

# 数据文件列表
datas = [
    # JavaScript文件
    (os.path.join(current_dir, 'main.js'), '.'),
    (os.path.join(current_dir, 'package.json'), '.'),
    
    # 图标文件
    (os.path.join(current_dir, 'logo.ico'), '.'),
    
    # node_modules目录
    (os.path.join(current_dir, 'node_modules'), 'node_modules'),
    
    # 资源目录（如果存在）
]

# 检查resources目录
resources_dir = os.path.join(current_dir, 'resources')
if os.path.exists(resources_dir):
    datas.append((resources_dir, 'resources'))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'sqlite3',
    'requests',
    'execjs',
    'ddddocr',
    'numpy',
    'PIL',
    'cryptography',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'IPython',
]

a = Analysis(
    [os.path.join(current_dir, 'app_main.py')],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='中国社会组织监控工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(current_dir, 'logo.ico'),
)
'''
    
    with open('app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ spec文件创建完成: app.spec")

def build_with_pyinstaller():
    """使用PyInstaller打包"""
    print("🚀 开始PyInstaller打包...")
    
    # PyInstaller命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',  # 清理临时文件
        '--noconfirm',  # 不询问覆盖
        'app.spec'  # 使用spec文件
    ]
    
    print("执行命令:")
    print(' '.join(cmd))
    print()
    
    try:
        subprocess.run(cmd, check=True, cwd=os.getcwd())
        print("✅ PyInstaller打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller打包失败: {e}")
        return False

def create_portable_version():
    """创建便携版"""
    print("📦 创建便携版...")
    
    exe_path = os.path.join('dist', '中国社会组织监控工具.exe')
    if not os.path.exists(exe_path):
        print("❌ 未找到生成的exe文件")
        return False
    
    # 创建便携版目录
    portable_dir = '便携版_PyInstaller'
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    os.makedirs(portable_dir)
    
    # 复制exe文件
    shutil.copy2(exe_path, portable_dir)
    
    # 复制配置文件模板
    if os.path.exists('便携版/config.ini.template'):
        shutil.copy2('便携版/config.ini.template', portable_dir)
    
    # 复制使用说明
    if os.path.exists('便携版/使用说明.txt'):
        shutil.copy2('便携版/使用说明.txt', portable_dir)
    
    # 获取文件大小
    exe_size = os.path.getsize(os.path.join(portable_dir, '中国社会组织监控工具.exe'))
    print(f"✅ 便携版创建完成: {portable_dir}/")
    print(f"   exe文件大小: {exe_size / (1024*1024):.1f} MB")
    
    return True

def cleanup():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    cleanup_dirs = ['build', '__pycache__']
    cleanup_files = ['app.spec']
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")
    
    for file_name in cleanup_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"   删除文件: {file_name}")

def main():
    """主函数"""
    print("🔨 中国社会组织监控工具 - PyInstaller打包脚本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 检查文件
    if not check_files():
        return False
    
    # 准备打包环境
    print("🔧 准备打包环境...")
    
    # 创建spec文件
    create_spec_file()
    
    # 开始打包
    if not build_with_pyinstaller():
        return False
    
    # 创建便携版
    if not create_portable_version():
        return False
    
    # 清理临时文件
    cleanup()
    
    print()
    print("🎉 打包完成！")
    print("=" * 50)
    print("📁 输出文件:")
    print("   - dist/中国社会组织监控工具.exe")
    print("   - 便携版_PyInstaller/中国社会组织监控工具.exe")
    print()
    print("✅ 所有JavaScript文件和node_modules已包含在exe中")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程中发生错误: {e}")
        sys.exit(1)
