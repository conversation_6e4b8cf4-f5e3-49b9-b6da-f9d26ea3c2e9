"""
日志管理模块
"""
import logging
import os
from logging.handlers import RotatingFileHandler
from PyQt5.QtCore import QObject, pyqtSignal
from datetime import datetime


class LogManager(QObject):
    """日志管理类"""
    
    # 信号定义
    log_message = pyqtSignal(str)  # 发送日志消息到GUI
    
    def __init__(self, log_file="monitor.log", max_size=10*1024*1024, backup_count=5):
        super().__init__()
        self.log_file = log_file
        self.max_size = max_size
        self.backup_count = backup_count
        self.setup_logger()
        
    def setup_logger(self):
        """设置日志记录器"""
        # 创建日志记录器
        self.logger = logging.getLogger('SocialOrgMonitor')
        self.logger.setLevel(logging.INFO)
        
        # 清除已有的处理器
        self.logger.handlers.clear()
        
        # 创建文件处理器（带轮转）
        file_handler = RotatingFileHandler(
            self.log_file,
            maxBytes=self.max_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
    def info(self, message):
        """记录信息日志"""
        self.logger.info(message)
        self._emit_to_gui("INFO", message)
        
    def warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)
        self._emit_to_gui("WARNING", message)
        
    def error(self, message):
        """记录错误日志"""
        self.logger.error(message)
        self._emit_to_gui("ERROR", message)
        
    def debug(self, message):
        """记录调试日志"""
        self.logger.debug(message)
        self._emit_to_gui("DEBUG", message)
        
    def _emit_to_gui(self, level, message):
        """发送日志到GUI"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {level}: {message}"
        self.log_message.emit(formatted_message)
        
    def set_level(self, level):
        """设置日志级别"""
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR
        }
        
        if level.upper() in level_map:
            self.logger.setLevel(level_map[level.upper()])
            
    def clear_logs(self):
        """清空日志文件"""
        try:
            if os.path.exists(self.log_file):
                open(self.log_file, 'w').close()
            self.info("日志文件已清空")
        except Exception as e:
            self.error(f"清空日志文件失败: {str(e)}")
            
    def get_log_size(self):
        """获取日志文件大小"""
        try:
            if os.path.exists(self.log_file):
                size = os.path.getsize(self.log_file)
                return self._format_size(size)
            return "0 B"
        except:
            return "未知"
            
    def _format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
