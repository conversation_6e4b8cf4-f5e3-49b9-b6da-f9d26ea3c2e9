#!/usr/bin/env python3
"""
测试代理重新获取周期功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.config import ConfigManager
from core.crawler import SocialOrgCrawler
from core.proxy_manager import ProxyManager
from core.kuaidaili_manager import KuaidailiManager

def test_proxy_retry_cycles():
    """测试代理重新获取周期功能"""
    print("🧪 测试代理重新获取周期功能...")
    print("=" * 60)
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 检查代理配置
    proxy_enabled = config_manager.is_proxy_enabled()
    kuaidaili_enabled = config_manager.is_kuaidaili_enabled()
    
    print(f"📋 代理配置状态:")
    print(f"   代理启用: {proxy_enabled}")
    print(f"   快代理API启用: {kuaidaili_enabled}")
    
    if not proxy_enabled or not kuaidaili_enabled:
        print("⚠️ 代理或快代理API未启用，无法测试重试周期功能")
        return
    
    # 初始化组件
    kuaidaili_manager = KuaidailiManager(config_manager)
    proxy_manager = ProxyManager(config_manager, kuaidaili_manager)
    crawler = SocialOrgCrawler(config_manager, proxy_manager)
    
    # 测试快代理API连接
    print("\n🔍 测试快代理API连接...")
    success, message = kuaidaili_manager.test_api_connection()
    
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
        return
    
    # 测试新的重试逻辑
    print("\n🔄 测试代理重新获取周期功能...")
    print("参数设置:")
    print("   关键词: 高级中学")
    print("   页面大小: 10")
    print("   每个代理最大重试次数: 5")
    print("   最大代理周期数: 2")
    print()
    
    # 调用新的重试方法
    result = crawler.try_with_retry(
        keyword="高级中学",
        page_size=10,
        max_retries=5,  # 每个代理重试5次
        max_proxy_cycles=2  # 最多2个代理周期
    )
    
    if result and result[0] is not None:
        data, success_attempt, total_count = result
        print(f"\n🎉 测试成功！")
        print(f"   成功尝试次数: {success_attempt}")
        print(f"   获取数据条数: {len(data) if data else 0}")
        print(f"   网站总记录数: {total_count}")
        
        if data and len(data) > 0:
            print(f"   示例数据: {data[0].get('aaae0103', '未知机构')[:20]}...")
    else:
        print(f"\n❌ 测试失败 - 所有代理周期都无法获取数据")
    
    print("\n🎉 代理重新获取周期功能测试完成！")

def test_single_proxy_cycle():
    """测试单个代理周期（用于对比）"""
    print("\n" + "=" * 60)
    print("🧪 测试单个代理周期（对比测试）...")
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    kuaidaili_manager = KuaidailiManager(config_manager)
    proxy_manager = ProxyManager(config_manager, kuaidaili_manager)
    crawler = SocialOrgCrawler(config_manager, proxy_manager)
    
    print("参数设置:")
    print("   关键词: 高级中学")
    print("   页面大小: 10")
    print("   每个代理最大重试次数: 10")
    print("   最大代理周期数: 1")
    print()
    
    # 调用新的重试方法（单周期）
    result = crawler.try_with_retry(
        keyword="高级中学",
        page_size=10,
        max_retries=10,  # 单个代理重试10次
        max_proxy_cycles=1  # 只有1个代理周期
    )
    
    if result and result[0] is not None:
        data, success_attempt, total_count = result
        print(f"\n🎉 单周期测试成功！")
        print(f"   成功尝试次数: {success_attempt}")
        print(f"   获取数据条数: {len(data) if data else 0}")
        print(f"   网站总记录数: {total_count}")
    else:
        print(f"\n❌ 单周期测试失败")

if __name__ == "__main__":
    try:
        test_proxy_retry_cycles()
        test_single_proxy_cycle()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
