#!/usr/bin/env python3
"""
测试代理验证功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(__file__))

from utils.config import ConfigManager
from core.proxy_manager import ProxyManager
from core.kuaidaili_manager import KuaidailiManager

def test_proxy_verification():
    """测试代理验证功能"""
    print("🧪 测试代理验证功能...")
    print("=" * 60)
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 检查代理配置
    proxy_enabled = config_manager.is_proxy_enabled()
    kuaidaili_enabled = config_manager.is_kuaidaili_enabled()
    
    print(f"📋 代理配置状态:")
    print(f"   代理启用: {proxy_enabled}")
    print(f"   快代理API启用: {kuaidaili_enabled}")
    
    if not proxy_enabled or not kuaidaili_enabled:
        print("⚠️ 代理或快代理API未启用，无法测试验证功能")
        return
    
    # 初始化组件
    kuaidaili_manager = KuaidailiManager(config_manager)
    proxy_manager = ProxyManager(config_manager, kuaidaili_manager)
    
    # 测试快代理API连接
    print("\n🔍 测试快代理API连接...")
    success, message = kuaidaili_manager.test_api_connection()
    
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
        return
    
    # 测试多个代理的验证
    print("\n🔄 测试代理验证功能...")
    print("获取5个代理进行验证测试...")
    
    test_count = 5
    success_count = 0
    failed_count = 0
    
    for i in range(test_count):
        print(f"\n--- 测试第 {i + 1} 个代理 ---")
        
        # 获取新的代理
        proxies = kuaidaili_manager.get_fresh_proxy(num=1)
        if not proxies:
            print("❌ 无法获取代理")
            failed_count += 1
            continue
        
        proxy_ip = proxies[0]
        print(f"🔄 获取到代理: {proxy_ip}")
        
        # 格式化代理配置
        if '://' not in proxy_ip:
            proxy_ip = 'http://' + proxy_ip
        
        proxy_config = {
            'http': proxy_ip,
            'https': proxy_ip
        }
        
        # 测试验证
        start_time = time.time()
        is_valid = proxy_manager.verify_proxy(proxy_config, timeout=6)
        end_time = time.time()
        
        verification_time = end_time - start_time
        
        if is_valid:
            success_count += 1
            print(f"✅ 验证成功 (耗时: {verification_time:.2f}秒)")
        else:
            failed_count += 1
            print(f"❌ 验证失败 (耗时: {verification_time:.2f}秒)")
        
        # 稍微等待一下再测试下一个
        if i < test_count - 1:
            print("⏳ 等待2秒后测试下一个代理...")
            time.sleep(2)
    
    # 统计结果
    print(f"\n📊 验证测试结果:")
    print(f"   总测试数: {test_count}")
    print(f"   验证成功: {success_count}")
    print(f"   验证失败: {failed_count}")
    print(f"   成功率: {success_count/test_count*100:.1f}%")
    
    if success_count > 0:
        print(f"✅ 代理验证功能正常，成功率为 {success_count/test_count*100:.1f}%")
    else:
        print(f"❌ 代理验证功能异常，所有代理都验证失败")
        print("💡 建议检查：")
        print("   1. 网络连接是否正常")
        print("   2. 快代理API返回的代理质量")
        print("   3. 百度网站是否可以正常访问")

def test_verification_methods():
    """测试不同的验证方法"""
    print("\n" + "=" * 60)
    print("🧪 测试不同验证方法的性能...")
    
    config_manager = ConfigManager()
    kuaidaili_manager = KuaidailiManager(config_manager)
    proxy_manager = ProxyManager(config_manager, kuaidaili_manager)
    
    # 获取一个代理进行测试
    proxies = kuaidaili_manager.get_fresh_proxy(num=1)
    if not proxies:
        print("❌ 无法获取代理进行测试")
        return
    
    proxy_ip = proxies[0]
    if '://' not in proxy_ip:
        proxy_ip = 'http://' + proxy_ip
    
    proxy_config = {
        'http': proxy_ip,
        'https': proxy_ip
    }
    
    print(f"🔄 使用代理 {proxy_ip.replace('http://', '')} 进行测试")
    
    # 测试快速验证
    print("\n1️⃣ 测试快速连接验证...")
    start_time = time.time()
    quick_result = proxy_manager._quick_proxy_test(proxy_config, timeout=3)
    quick_time = time.time() - start_time
    print(f"   快速验证结果: {'✅ 成功' if quick_result else '❌ 失败'}")
    print(f"   耗时: {quick_time:.2f}秒")
    
    # 测试完整验证
    print("\n2️⃣ 测试完整HTTP验证...")
    start_time = time.time()
    full_result = proxy_manager._full_proxy_test(proxy_config, proxy_ip.replace('http://', ''), timeout=6)
    full_time = time.time() - start_time
    print(f"   完整验证结果: {'✅ 成功' if full_result else '❌ 失败'}")
    print(f"   耗时: {full_time:.2f}秒")
    
    # 测试综合验证
    print("\n3️⃣ 测试综合验证...")
    start_time = time.time()
    combined_result = proxy_manager.verify_proxy(proxy_config, timeout=6)
    combined_time = time.time() - start_time
    print(f"   综合验证结果: {'✅ 成功' if combined_result else '❌ 失败'}")
    print(f"   耗时: {combined_time:.2f}秒")
    
    print(f"\n📊 验证方法性能对比:")
    print(f"   快速验证: {quick_time:.2f}秒")
    print(f"   完整验证: {full_time:.2f}秒")
    print(f"   综合验证: {combined_time:.2f}秒")

if __name__ == "__main__":
    try:
        test_proxy_verification()
        test_verification_methods()
        print("\n🎉 代理验证功能测试完成！")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
