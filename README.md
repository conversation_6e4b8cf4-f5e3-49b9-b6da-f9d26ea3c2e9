# 中国社会组织监控工具

一个用于监控中国社会组织政务服务平台数据变化的桌面应用程序。

## 功能特点

- 🔍 **智能监控**: 自动监控"高级中学"和"中学"关键词的数据变化
- 📊 **数据管理**: SQLite数据库存储，支持独立表管理不同关键词
- 🔔 **实时通知**: 企业微信机器人通知新增数据
- 🕒 **定时任务**: 5-6分钟随机间隔检查，支持时间段控制
- 🌐 **代理支持**: 内置代理池管理，支持代理轮换和测试
- 🖥️ **友好界面**: PyQt5图形界面，支持系统托盘
- 🔒 **防多开**: 单实例运行，防止重复启动
- 📝 **日志记录**: 完整的运行日志和状态监控

## 系统要求

- Windows 10/11
- Python 3.7+
- 网络连接

## 安装依赖

```bash
pip install -r requirements.txt
```

依赖包列表：
- PyQt5>=5.15.0 - GUI界面
- requests>=2.25.0 - HTTP请求
- ddddocr>=1.4.0 - 验证码识别
- PyExecJS>=1.5.0 - JavaScript执行
- configparser>=5.0.0 - 配置文件管理
- Pillow>=8.0.0 - 图像处理

## 使用方法

### 开发环境运行

```bash
python run_test.py
```

### 配置说明

1. **监控关键词**: 选择要监控的关键词（高级中学/中学）
2. **微信通知**: 配置企业微信机器人webhook地址
3. **监控时间**: 设置监控的时间段（如09:00-23:00）
4. **代理配置**: 可选配置代理池提高稳定性

### 企业微信配置

1. 在企业微信群中添加机器人
2. 获取webhook地址
3. 在程序中配置webhook地址
4. 点击"测试"按钮验证连接

### 代理配置

1. 点击"代理配置"按钮
2. 输入代理地址，格式：`ip:port` 或 `http://ip:port`
3. 点击"测试所有代理"验证可用性
4. 启用代理池并保存配置

## 项目结构

```
├── ui/                     # GUI界面模块
│   ├── main_window.py     # 主窗口
│   ├── config_dialog.py   # 配置对话框
│   └── proxy_dialog.py    # 代理配置对话框
├── core/                   # 核心功能模块
│   ├── monitor.py         # 监控核心逻辑
│   ├── crawler.py         # 爬虫功能
│   ├── database.py        # 数据库管理
│   ├── notification.py    # 通知管理
│   └── proxy_manager.py   # 代理池管理
├── utils/                  # 工具模块
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志管理
│   ├── resource_manager.py # 资源管理
│   └── single_instance.py # 防多开
├── resources/              # 资源文件
├── app_main.py            # 主程序入口
├── run_test.py            # 测试运行脚本
└── requirements.txt       # 依赖包列表
```

## 技术架构

- **GUI框架**: PyQt5
- **数据库**: SQLite
- **验证码识别**: ddddocr
- **JavaScript执行**: PyExecJS + Node.js
- **HTTP请求**: requests + 代理池
- **配置管理**: configparser (INI格式)
- **日志系统**: logging + 文件轮转

## 开发状态

当前版本：v1.0 (开发版)

- ✅ 核心功能开发完成
- ✅ GUI界面开发完成
- ✅ 功能集成完成
- ⏳ 依赖安装和测试中
- ⏳ 打包配置准备中

## 注意事项

1. 首次运行需要安装所有依赖包
2. 需要稳定的网络连接访问目标网站
3. 建议配置代理池提高稳定性
4. 程序会在C盘创建运行时目录存放临时文件
5. 关闭窗口会最小化到系统托盘，不会退出程序

## 故障排除

### 常见问题

1. **导入错误**: 确保已安装所有依赖包
2. **验证码识别失败**: 检查网络连接和代理配置
3. **通知发送失败**: 验证企业微信webhook地址
4. **程序无法启动**: 检查是否已有实例在运行

### 日志查看

程序运行日志保存在 `monitor.log` 文件中，可以查看详细的运行信息和错误记录。

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。

## 更新日志

### v1.0 (2024-06-16)
- 初始版本发布
- 实现核心监控功能
- 完成GUI界面开发
- 支持企业微信通知
- 集成代理池管理
