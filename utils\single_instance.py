"""
防多开模块
"""
import os
import sys
import tempfile
import subprocess
from PyQt5.QtWidgets import QMessageBox


class SingleInstance:
    """单实例类 - 防止程序多开"""

    def __init__(self, app_name="SocialOrgMonitor"):
        self.app_name = app_name
        self.lock_file = None
        self.lock_fd = None

        # 在Windows上使用不同的实现
        if sys.platform == "win32":
            self._init_windows()
        else:
            self._init_unix()

    def _is_process_running(self, pid):
        """检查进程是否在运行"""
        try:
            if sys.platform == "win32":
                import subprocess
                result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'],
                                      capture_output=True, text=True)
                return str(pid) in result.stdout
            else:
                os.kill(pid, 0)
                return True
        except (OSError, subprocess.SubprocessError):
            return False

    def _init_windows(self):
        """Windows平台初始化"""
        # 使用临时目录创建锁文件
        lock_file_path = os.path.join(tempfile.gettempdir(), f"{self.app_name}.lock")

        # 首先检查是否有残留的锁文件，如果有就尝试删除
        if os.path.exists(lock_file_path):
            try:
                # 尝试删除可能的残留锁文件
                os.unlink(lock_file_path)
                print(f"清理了残留的锁文件: {lock_file_path}")
            except OSError:
                pass  # 如果删除失败，可能是真的有程序在运行

        try:
            # 尝试创建并锁定文件
            self.lock_fd = os.open(lock_file_path, os.O_CREAT | os.O_EXCL | os.O_RDWR)
            self.lock_file = lock_file_path
            # 写入当前进程ID
            os.write(self.lock_fd, str(os.getpid()).encode())
        except OSError:
            # 文件已存在，检查是否是真的有程序在运行
            if os.path.exists(lock_file_path):
                try:
                    with open(lock_file_path, 'r') as f:
                        pid = f.read().strip()
                    if pid and self._is_process_running(int(pid)):
                        # 进程确实在运行
                        self.lock_fd = None
                        self.lock_file = None
                    else:
                        # 进程不存在，删除锁文件并重新创建
                        os.unlink(lock_file_path)
                        self.lock_fd = os.open(lock_file_path, os.O_CREAT | os.O_EXCL | os.O_RDWR)
                        self.lock_file = lock_file_path
                        os.write(self.lock_fd, str(os.getpid()).encode())
                except (ValueError, OSError):
                    # 如果读取失败，假设程序在运行
                    self.lock_fd = None
                    self.lock_file = None
            else:
                self.lock_fd = None
                self.lock_file = None

    def _init_unix(self):
        """Unix/Linux平台初始化"""
        try:
            import fcntl
        except ImportError:
            # 如果没有fcntl模块，使用简单的文件检查
            self._init_windows()
            return

        # 使用临时目录创建锁文件
        lock_file_path = os.path.join(tempfile.gettempdir(), f"{self.app_name}.lock")

        try:
            self.lock_fd = open(lock_file_path, 'w')
            fcntl.lockf(self.lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
            self.lock_file = lock_file_path
        except (IOError, OSError):
            # 无法获取锁，说明程序已在运行
            if self.lock_fd:
                self.lock_fd.close()
            self.lock_fd = None
            self.lock_file = None
            
    def is_running(self):
        """检查程序是否已在运行"""
        return self.lock_fd is None
        
    def show_already_running_message(self):
        """显示程序已运行的提示"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("程序已运行")
        msg.setText("中国社会组织监控工具已在运行中！")
        msg.setInformativeText("请检查系统托盘或任务管理器。")
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()
        
    def cleanup(self):
        """清理资源"""
        try:
            if self.lock_fd is not None:
                if sys.platform == "win32":
                    os.close(self.lock_fd)
                else:
                    self.lock_fd.close()
                    
            if self.lock_file and os.path.exists(self.lock_file):
                os.unlink(self.lock_file)
                
        except Exception as e:
            print(f"清理单实例锁文件时出错: {e}")
            
    def __del__(self):
        """析构函数"""
        self.cleanup()
