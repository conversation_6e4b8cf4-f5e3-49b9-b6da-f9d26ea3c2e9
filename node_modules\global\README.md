# global

<!-- [![build status][1]][2]

[![browser support][3]][4] -->

Require global variables

## Example

```js
var global = require("global")
var document = require("global/document")
var window = require("global/window")
```

## Installation

`npm install global`

## Contributors

 - Raynos

## MIT Licenced

  [1]: https://secure.travis-ci.org/Colingo/global.png
  [2]: http://travis-ci.org/Colingo/global
  [3]: http://ci.testling.com/Colingo/global.png
  [4]: http://ci.testling.com/Colingo/global
