"""
检查数据库记录数
"""
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.database import DatabaseManager

def check_database():
    """检查数据库记录数"""
    print("=== 检查数据库记录数 ===\n")
    
    db_manager = DatabaseManager()
    
    keywords = ["高级中学", "中学"]
    
    for keyword in keywords:
        try:
            count = db_manager.get_record_count(keyword)
            print(f"{keyword}: {count} 条记录")
            
            # 显示一些示例数据
            if count > 0:
                existing_codes = db_manager.get_existing_codes(keyword)
                print(f"   已有组织代码数量: {len(existing_codes)}")
                if existing_codes:
                    sample_codes = list(existing_codes)[:3]
                    print(f"   示例代码: {sample_codes}")
            
        except Exception as e:
            print(f"{keyword}: 数据库表可能不存在 - {e}")
    
    print(f"\n数据库文件位置: {db_manager.db_path}")

if __name__ == "__main__":
    check_database()
