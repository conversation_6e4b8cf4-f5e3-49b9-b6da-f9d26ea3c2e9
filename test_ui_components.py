"""
测试快代理SDK UI组件
"""
import sys
import os

# 添加SDK路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'kuaidaili_sdk'))

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
    from PyQt5.QtCore import Qt
    
    # 测试导入UI组件
    from kuaidaili_sdk.ui import ProxyDialog, ProxyWidget, ConfigWidget
    from kuaidaili_sdk import KuaidailiConfig
    
    print("✅ 所有UI组件导入成功！")
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("快代理SDK UI组件测试")
            self.setGeometry(100, 100, 400, 300)
            
            central_widget = QWidget()
            layout = QVBoxLayout(central_widget)
            
            # 标题
            title = QLabel("快代理SDK UI组件测试")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("font-size: 16px; font-weight: bold; color: #007bff; padding: 10px;")
            layout.addWidget(title)
            
            # 测试按钮
            btn_dialog = QPushButton("测试 ProxyDialog")
            btn_dialog.clicked.connect(self.test_dialog)
            layout.addWidget(btn_dialog)
            
            btn_widget = QPushButton("测试 ProxyWidget")
            btn_widget.clicked.connect(self.test_widget)
            layout.addWidget(btn_widget)
            
            btn_config = QPushButton("测试 ConfigWidget")
            btn_config.clicked.connect(self.test_config)
            layout.addWidget(btn_config)
            
            # 状态标签
            self.status_label = QLabel("就绪")
            self.status_label.setAlignment(Qt.AlignCenter)
            self.status_label.setStyleSheet("padding: 10px; border: 1px solid #dee2e6; border-radius: 4px;")
            layout.addWidget(self.status_label)
            
            self.setCentralWidget(central_widget)
        
        def test_dialog(self):
            """测试对话框"""
            try:
                dialog = ProxyDialog(self)
                self.status_label.setText("ProxyDialog 创建成功！点击确定测试...")
                
                if dialog.exec_() == ProxyDialog.Accepted:
                    config = dialog.get_config()
                    self.status_label.setText(f"对话框测试完成，配置有效: {config.is_valid()}")
                else:
                    self.status_label.setText("用户取消了对话框")
            except Exception as e:
                self.status_label.setText(f"对话框测试失败: {str(e)}")
        
        def test_widget(self):
            """测试组件"""
            try:
                # 创建新窗口显示组件
                widget_window = QMainWindow(self)
                widget_window.setWindowTitle("ProxyWidget 测试")
                widget_window.setGeometry(150, 150, 600, 500)
                
                proxy_widget = ProxyWidget()
                proxy_widget.config_changed.connect(
                    lambda config: self.status_label.setText(f"ProxyWidget 配置变化: {config.is_valid()}")
                )
                proxy_widget.status_changed.connect(
                    lambda status: self.status_label.setText(f"ProxyWidget 状态: {status}")
                )
                
                widget_window.setCentralWidget(proxy_widget)
                widget_window.show()
                
                self.status_label.setText("ProxyWidget 测试窗口已打开")
                
            except Exception as e:
                self.status_label.setText(f"组件测试失败: {str(e)}")
        
        def test_config(self):
            """测试简化配置组件"""
            try:
                # 创建新窗口显示组件
                config_window = QMainWindow(self)
                config_window.setWindowTitle("ConfigWidget 测试")
                config_window.setGeometry(200, 200, 400, 300)
                
                config_widget = ConfigWidget()
                config_widget.config_changed.connect(
                    lambda config: self.status_label.setText(f"ConfigWidget 配置变化: {config.is_valid()}")
                )
                config_widget.test_completed.connect(
                    lambda success, msg: self.status_label.setText(f"ConfigWidget 测试: {'成功' if success else '失败'}")
                )
                
                config_window.setCentralWidget(config_widget)
                config_window.show()
                
                self.status_label.setText("ConfigWidget 测试窗口已打开")
                
            except Exception as e:
                self.status_label.setText(f"简化组件测试失败: {str(e)}")
    
    def main():
        app = QApplication(sys.argv)
        
        # 设置应用样式
        app.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        
        window = TestWindow()
        window.show()
        
        print("🎉 UI测试窗口已打开！")
        print("请在窗口中测试各个组件...")
        
        sys.exit(app.exec_())
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("可能的原因:")
    print("1. PyQt5 未安装")
    print("2. 路径配置问题")
    print("3. 模块依赖问题")
    
    # 测试基本SDK功能
    try:
        from kuaidaili_sdk import KuaidailiClient, KuaidailiConfig
        print("✅ 核心SDK功能正常")
        
        config = KuaidailiConfig(secret_id="test", signature="test")
        client = KuaidailiClient(config=config)
        print("✅ 客户端创建成功")
        
    except Exception as e2:
        print(f"❌ 核心SDK也有问题: {e2}")

except Exception as e:
    print(f"❌ 其他错误: {e}")
