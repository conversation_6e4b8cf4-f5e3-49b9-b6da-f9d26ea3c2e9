"""
快代理SDK配置管理
"""
import os
import configparser
from typing import Optional, Dict, Any
from .exceptions import ConfigException


class KuaidailiConfig:
    """快代理配置管理类"""
    
    def __init__(self, config_file: Optional[str] = None, 
                 secret_id: Optional[str] = None,
                 signature: Optional[str] = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径
            secret_id: 快代理Secret ID
            signature: 快代理Signature
        """
        self.config_file = config_file
        self._secret_id = secret_id
        self._signature = signature
        self._config = None
        
        # 如果提供了配置文件，则加载
        if config_file:
            self.load_from_file(config_file)
    
    @classmethod
    def from_env(cls) -> 'KuaidailiConfig':
        """从环境变量创建配置"""
        secret_id = os.getenv('KUAIDAILI_SECRET_ID')
        signature = os.getenv('KUAIDAILI_SIGNATURE')
        
        if not secret_id or not signature:
            raise ConfigException("环境变量 KUAIDAILI_SECRET_ID 和 KUAIDAILI_SIGNATURE 必须设置")
        
        return cls(secret_id=secret_id, signature=signature)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'KuaidailiConfig':
        """从字典创建配置"""
        secret_id = config_dict.get('secret_id')
        signature = config_dict.get('signature')
        
        if not secret_id or not signature:
            raise ConfigException("配置字典必须包含 secret_id 和 signature")
        
        return cls(secret_id=secret_id, signature=signature)
    
    def load_from_file(self, config_file: str):
        """从配置文件加载"""
        if not os.path.exists(config_file):
            raise ConfigException(f"配置文件不存在: {config_file}")
        
        self._config = configparser.ConfigParser()
        self._config.read(config_file, encoding='utf-8')
        
        # 尝试从不同的section读取配置
        sections_to_try = ['Kuaidaili', 'kuaidaili', 'KUAIDAILI']
        
        for section in sections_to_try:
            if self._config.has_section(section):
                self._secret_id = self._config.get(section, 'secret_id', fallback=None)
                self._signature = self._config.get(section, 'signature', fallback=None)
                break
        
        if not self._secret_id or not self._signature:
            raise ConfigException("配置文件中未找到有效的 secret_id 和 signature")
    
    @property
    def secret_id(self) -> str:
        """获取Secret ID"""
        if not self._secret_id:
            raise ConfigException("Secret ID 未配置")
        return self._secret_id
    
    @property
    def signature(self) -> str:
        """获取Signature"""
        if not self._signature:
            raise ConfigException("Signature 未配置")
        return self._signature
    
    def is_valid(self) -> bool:
        """检查配置是否有效"""
        return bool(self._secret_id and self._signature)
    
    def get_api_credentials(self) -> tuple:
        """获取API凭证"""
        return self.secret_id, self.signature
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典"""
        return {
            'secret_id': self._secret_id or '',
            'signature': self._signature or ''
        }
