"""
主窗口模块
"""
import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QGroupBox, QCheckBox, QLineEdit, QLabel, QPushButton,
                             QTextEdit, QTimeEdit, QSystemTrayIcon, QMenu, QAction,
                             QMessageBox, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal, QTime
from PyQt5.QtGui import QIcon, QFont


class LeftAlignedLineEdit(QLineEdit):
    """强制左对齐的QLineEdit"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

    def setText(self, text):
        super().setText(text)
        self.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.setCursorPosition(0)  # 确保光标在开头

from core.monitor import MonitorCore
from utils.config import ConfigManager
from utils.logger import LogManager
from ui.config_dialog import ConfigDialog
from ui.proxy_dialog import ProxyDialog


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.monitor_core = MonitorCore()
        # 让监控核心使用同一个配置管理器实例
        self.monitor_core.config_manager = self.config_manager
        self.monitor_core.proxy_manager.config_manager = self.config_manager
        self.monitor_core.notification_manager.config_manager = self.config_manager
        self.log_manager = LogManager()

        self.init_ui()
        self.init_tray()
        self.load_config()  # 先加载配置
        self.connect_signals()  # 再连接信号，避免加载时触发保存

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("关键词监控工具 v1.0")
        self.setGeometry(100, 100, 900, 1000)  # 调整为合适的默认宽度
        self.setMinimumSize(800, 800)  # 设置最小窗口尺寸，确保界面元素正常显示

        # 设置窗口图标
        self.set_window_icon()

        # 设置整体字体
        font = QFont("Microsoft YaHei UI", 10)  # 使用微软雅黑，字体大小10
        self.setFont(font)

        # 设置整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #495057;
                background-color: #ffffff;
            }
            QLineEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 10pt;
                background-color: #ffffff;
                text-align: left;
            }
            QLineEdit:focus {
                border-color: #007bff;
                border-width: 2px;
            }
            QTimeEdit {
                min-height: 35px;
                max-height: 35px;
                font-size: 10pt;
            }
            QCheckBox {
                spacing: 8px;
                font-size: 10pt;
            }
        """)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局，增加边距
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)  # 增加边距
        main_layout.setSpacing(15)  # 增加组件间距

        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)

        # 配置区域
        config_group = self.create_config_group()
        splitter.addWidget(config_group)

        # 控制区域
        control_group = self.create_control_group()
        splitter.addWidget(control_group)

        # 日志区域
        log_group = self.create_log_group()
        splitter.addWidget(log_group)

        # 设置分割器比例 - 大幅增加日志区域高度
        splitter.setStretchFactor(0, 1)  # 配置区域
        splitter.setStretchFactor(1, 0)  # 控制区域（固定高度）
        splitter.setStretchFactor(2, 7)  # 日志区域（大幅增加比例到7）

        # 设置初始分割器尺寸 - 给日志区域更多空间
        splitter.setSizes([180, 80, 700])  # 配置区域180px，控制区域80px，日志区域700px

        # 强制设置分割器的收缩策略
        splitter.setChildrenCollapsible(False)  # 防止子组件被完全折叠

        # 创建状态栏
        self.statusBar().showMessage("就绪")
        self.statusBar().setFont(QFont("Microsoft YaHei UI", 9))

    def create_config_group(self):
        """创建配置区域"""
        group = QGroupBox("监控配置")
        group.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        layout = QVBoxLayout(group)
        layout.setContentsMargins(20, 20, 20, 20)  # 增加内边距
        layout.setSpacing(15)  # 增加组件间距

        # 关键词选择
        keyword_layout = QHBoxLayout()
        keyword_layout.setSpacing(15)

        keyword_label = QLabel("监控关键词:")
        keyword_label.setFont(QFont("Microsoft YaHei UI", 10))
        keyword_label.setMinimumWidth(100)
        keyword_layout.addWidget(keyword_label)

        self.cb_gaoji = QCheckBox("高级中学")
        self.cb_gaoji.setFont(QFont("Microsoft YaHei UI", 10))
        self.cb_zhong = QCheckBox("中学")
        self.cb_zhong.setFont(QFont("Microsoft YaHei UI", 10))

        keyword_layout.addWidget(self.cb_gaoji)
        keyword_layout.addWidget(self.cb_zhong)
        keyword_layout.addStretch()

        layout.addLayout(keyword_layout)

        # 微信配置
        wechat_layout = QHBoxLayout()
        wechat_layout.setSpacing(10)

        wechat_label = QLabel("微信Webhook:")
        wechat_label.setFont(QFont("Microsoft YaHei UI", 10))
        wechat_label.setMinimumWidth(100)
        wechat_layout.addWidget(wechat_label)

        self.le_webhook = QLineEdit()
        self.le_webhook.setFont(QFont("Microsoft YaHei UI", 10))
        self.le_webhook.setPlaceholderText("请输入企业微信机器人webhook地址")
        self.le_webhook.setMinimumHeight(35)  # 调整高度到35px
        self.le_webhook.setMaximumHeight(35)  # 固定高度

        # 强制设置文本左对齐
        self.le_webhook.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # 专门为webhook输入框设置样式，使用qproperty-alignment强制左对齐
        self.le_webhook.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ced4da;
                border-radius: 5px;
                padding: 8px 12px;
                font-size: 10pt;
                background-color: #ffffff;
                qproperty-alignment: 'AlignLeft | AlignVCenter';
                min-height: 35px;
                max-height: 35px;
            }
            QLineEdit:focus {
                border-color: #007bff;
                border-width: 2px;
            }
        """)

        self.btn_test_webhook = QPushButton("测试")
        self.btn_test_webhook.setFont(QFont("Microsoft YaHei UI", 10))
        self.btn_test_webhook.setMinimumSize(70, 30)
        self.btn_test_webhook.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)

        wechat_layout.addWidget(self.le_webhook)
        wechat_layout.addWidget(self.btn_test_webhook)

        layout.addLayout(wechat_layout)

        # 时间配置
        time_layout = QHBoxLayout()
        time_layout.setSpacing(10)

        time_label = QLabel("监控时间:")
        time_label.setFont(QFont("Microsoft YaHei UI", 10))
        time_label.setMinimumWidth(100)
        time_layout.addWidget(time_label)

        self.te_start_time = QTimeEdit()
        self.te_start_time.setFont(QFont("Microsoft YaHei UI", 10))
        self.te_start_time.setTime(QTime(0, 0))  # 默认00:00，支持24小时监控
        self.te_start_time.setMinimumHeight(35)  # 增加高度到35px
        self.te_start_time.setMaximumHeight(35)  # 固定高度
        self.te_start_time.setMinimumWidth(80)   # 设置最小宽度
        time_layout.addWidget(self.te_start_time)

        time_to_label = QLabel("至")
        time_to_label.setFont(QFont("Microsoft YaHei UI", 10))
        time_layout.addWidget(time_to_label)

        self.te_end_time = QTimeEdit()
        self.te_end_time.setFont(QFont("Microsoft YaHei UI", 10))
        self.te_end_time.setTime(QTime(23, 59))  # 默认23:59，支持24小时监控
        self.te_end_time.setMinimumHeight(35)    # 增加高度到35px
        self.te_end_time.setMaximumHeight(35)    # 固定高度
        self.te_end_time.setMinimumWidth(80)     # 设置最小宽度
        time_layout.addWidget(self.te_end_time)

        time_layout.addStretch()

        # 代理配置按钮
        self.btn_proxy_config = QPushButton("代理配置")
        self.btn_proxy_config.setFont(QFont("Microsoft YaHei UI", 10))
        self.btn_proxy_config.setMinimumSize(90, 30)
        self.btn_proxy_config.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        time_layout.addWidget(self.btn_proxy_config)

        layout.addLayout(time_layout)

        return group

    def create_control_group(self):
        """创建控制区域"""
        group = QGroupBox("监控控制")
        group.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        layout = QHBoxLayout(group)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        self.btn_start = QPushButton("开始监控")
        self.btn_start.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        self.btn_start.setMinimumSize(120, 40)
        self.btn_start.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.btn_stop = QPushButton("停止监控")
        self.btn_stop.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        self.btn_stop.setMinimumSize(120, 40)
        self.btn_stop.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)
        self.btn_stop.setEnabled(False)



        self.lbl_status = QLabel("状态: 未启动")
        self.lbl_status.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        self.lbl_status.setStyleSheet("""
            QLabel {
                color: #6c757d;
                padding: 8px 12px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """)

        layout.addWidget(self.btn_start)
        layout.addWidget(self.btn_stop)
        layout.addStretch()
        layout.addWidget(self.lbl_status)

        return group

    def create_log_group(self):
        """创建日志区域"""
        group = QGroupBox("运行日志")
        group.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        layout = QVBoxLayout(group)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        # 日志控制按钮
        log_control_layout = QHBoxLayout()
        log_control_layout.setSpacing(10)

        self.btn_clear_log = QPushButton("清空日志")
        self.btn_clear_log.setFont(QFont("Microsoft YaHei UI", 10))
        self.btn_clear_log.setMinimumSize(90, 30)
        self.btn_clear_log.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
            QPushButton:pressed {
                background-color: #d39e00;
            }
        """)

        log_control_layout.addStretch()
        log_control_layout.addWidget(self.btn_clear_log)

        layout.addLayout(log_control_layout)

        # 日志文本框
        self.te_log = QTextEdit()
        self.te_log.setReadOnly(True)
        self.te_log.setFont(QFont("Consolas", 10))  # 增大字体
        self.te_log.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                selection-background-color: #007bff;
                selection-color: white;
            }
        """)

        # 尝试设置最大行数限制（某些PyQt5版本可能不支持）
        try:
            self.te_log.setMaximumBlockCount(1000)
        except AttributeError:
            pass  # 如果不支持就忽略

        layout.addWidget(self.te_log)

        return group

    def set_window_icon(self):
        """设置窗口图标"""
        import os
        try:
            # 优先使用 logo.ico
            if os.path.exists("logo.ico"):
                self.setWindowIcon(QIcon("logo.ico"))
                print("使用 logo.ico 作为窗口图标")
            elif os.path.exists("icon.ico"):
                self.setWindowIcon(QIcon("icon.ico"))
                print("使用 icon.ico 作为窗口图标")
            elif os.path.exists("icon.png"):
                self.setWindowIcon(QIcon("icon.png"))
                print("使用 icon.png 作为窗口图标")
            else:
                # 使用系统默认图标
                from PyQt5.QtWidgets import QStyle
                icon = self.style().standardIcon(QStyle.SP_ComputerIcon)
                self.setWindowIcon(icon)
                print("使用系统默认图标作为窗口图标")
        except Exception as e:
            print(f"设置窗口图标失败: {e}")

    def init_tray(self):
        """初始化系统托盘"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            QMessageBox.critical(self, "系统托盘", "系统不支持托盘功能")
            return

        # 创建托盘图标
        self.tray_icon = QSystemTrayIcon(self)

        # 尝试设置图标，优先使用 logo.ico
        try:
            if os.path.exists("logo.ico"):
                self.tray_icon.setIcon(QIcon("logo.ico"))
                print("使用 logo.ico 作为托盘图标")
            elif os.path.exists("icon.ico"):
                self.tray_icon.setIcon(QIcon("icon.ico"))
                print("使用 icon.ico 作为托盘图标")
            elif os.path.exists("icon.png"):
                self.tray_icon.setIcon(QIcon("icon.png"))
                print("使用 icon.png 作为托盘图标")
            else:
                # 使用系统默认图标
                from PyQt5.QtWidgets import QStyle
                icon = self.style().standardIcon(QStyle.SP_ComputerIcon)
                self.tray_icon.setIcon(icon)
                print("使用系统默认图标作为托盘图标")
        except Exception as e:
            print(f"设置托盘图标失败: {e}")

        # 创建托盘菜单
        tray_menu = QMenu()

        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_window)

        quit_action = QAction("退出程序", self)
        quit_action.triggered.connect(self.quit_application)

        tray_menu.addAction(show_action)
        tray_menu.addSeparator()
        tray_menu.addAction(quit_action)

        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)

        # 显示托盘图标
        self.tray_icon.show()

    def connect_signals(self):
        """连接信号槽"""
        # 按钮信号
        self.btn_start.clicked.connect(self.start_monitor)
        self.btn_stop.clicked.connect(self.stop_monitor)
        self.btn_test_webhook.clicked.connect(self.test_webhook)
        self.btn_proxy_config.clicked.connect(self.show_proxy_dialog)
        self.btn_clear_log.clicked.connect(self.clear_log)

        # 配置变化信号 - 启用关键词和webhook的实时保存
        self.cb_gaoji.stateChanged.connect(self.save_keywords_only)
        self.cb_zhong.stateChanged.connect(self.save_keywords_only)
        self.le_webhook.textChanged.connect(self.save_webhook_only)
        # self.te_start_time.timeChanged.connect(self.save_config)
        # self.te_end_time.timeChanged.connect(self.save_config)

        # 时间验证信号
        self.te_start_time.timeChanged.connect(self.validate_start_time)
        self.te_end_time.timeChanged.connect(self.validate_end_time)

        # 监控核心信号
        self.monitor_core.status_changed.connect(self.update_status)
        self.monitor_core.log_message.connect(self.add_log)
        self.monitor_core.new_data_found.connect(self.handle_new_data)

        # 日志管理器信号
        self.log_manager.log_message.connect(self.add_log)

    def load_config(self):
        """加载配置"""
        # 临时断开信号连接，避免加载时触发保存
        try:
            self.cb_gaoji.stateChanged.disconnect()
            self.cb_zhong.stateChanged.disconnect()
        except:
            pass  # 如果信号还没有连接，忽略错误

        # 加载关键词配置
        keywords = self.config_manager.get_keywords()
        print(f"加载关键词配置: {keywords}")  # 调试信息
        self.cb_gaoji.setChecked("高级中学" in keywords)
        self.cb_zhong.setChecked("中学" in keywords)
        print(f"复选框状态 - 高级中学: {self.cb_gaoji.isChecked()}, 中学: {self.cb_zhong.isChecked()}")  # 调试信息

        # 不在这里重新连接信号，让connect_signals()方法统一处理

        # 临时断开其他控件的信号连接
        try:
            self.le_webhook.textChanged.disconnect()
            self.te_start_time.timeChanged.disconnect()
            self.te_end_time.timeChanged.disconnect()
        except:
            pass  # 如果信号还没有连接，忽略错误

        # 加载webhook配置
        webhook_url = self.config_manager.get_webhook_url()
        print(f"加载webhook配置: '{webhook_url}'")  # 调试信息
        self.le_webhook.setText(webhook_url)

        # 强制设置文本左对齐（在设置文本后）
        self.le_webhook.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.le_webhook.setCursorPosition(0)  # 将光标移到开头，确保显示开头部分

        # 加载时间配置
        start_time_str, end_time_str = self.config_manager.get_monitor_time()
        try:
            start_time = QTime.fromString(start_time_str, "hh:mm")
            end_time = QTime.fromString(end_time_str, "hh:mm")

            # 移除时间范围限制，允许24小时监控
            # 只需确保开始时间早于结束时间
            if start_time >= end_time:
                start_time = QTime(0, 0)
                end_time = QTime(23, 59)
                print(f"时间顺序错误，已重置为00:00-23:59")

            self.te_start_time.setTime(start_time)
            self.te_end_time.setTime(end_time)
        except:
            # 如果解析失败，使用默认时间（支持24小时监控）
            self.te_start_time.setTime(QTime(0, 0))
            self.te_end_time.setTime(QTime(23, 59))

        # 不在这里重新连接信号，让connect_signals()方法统一处理

    def save_config(self):
        """保存配置（只保存主窗口相关的配置，不覆盖代理配置）"""
        # 先重新读取配置文件，确保不覆盖其他配置
        self.config_manager.load_config()

        # 保存关键词配置
        keywords = []
        if self.cb_gaoji.isChecked():
            keywords.append("高级中学")
        if self.cb_zhong.isChecked():
            keywords.append("中学")
        print(f"保存关键词配置: {keywords}")  # 调试信息
        self.config_manager.set_keywords(keywords)

        # 保存webhook配置
        webhook_url = self.le_webhook.text().strip()
        print(f"保存webhook配置: '{webhook_url}'")  # 调试信息
        self.config_manager.set_webhook_url(webhook_url)

        # 保存时间配置
        start_time = self.te_start_time.time().toString("hh:mm")
        end_time = self.te_end_time.time().toString("hh:mm")
        self.config_manager.set_monitor_time(start_time, end_time)

    def save_keywords_only(self):
        """只保存关键词配置，不影响其他配置"""
        # 先重新读取配置文件，确保不覆盖其他配置
        self.config_manager.load_config()

        # 只保存关键词配置
        keywords = []
        if self.cb_gaoji.isChecked():
            keywords.append("高级中学")
        if self.cb_zhong.isChecked():
            keywords.append("中学")
        print(f"实时保存关键词配置: {keywords}")  # 调试信息
        self.config_manager.set_keywords(keywords)

    def save_webhook_only(self):
        """只保存webhook配置，不影响其他配置"""
        # 先重新读取配置文件，确保不覆盖其他配置
        self.config_manager.load_config()

        # 只保存webhook配置
        webhook_url = self.le_webhook.text().strip()
        print(f"实时保存webhook配置: '{webhook_url}'")  # 调试信息
        self.config_manager.set_webhook_url(webhook_url)

    def validate_start_time(self, time):
        """验证开始时间"""
        # 移除8-23点限制，只检查是否晚于结束时间
        if time >= self.te_end_time.time():
            QMessageBox.warning(
                self,
                "时间设置错误",
                "监控开始时间不能晚于或等于结束时间！\n已自动调整为00:00"
            )
            # 临时断开信号，避免递归调用
            self.te_start_time.timeChanged.disconnect()
            self.te_start_time.setTime(QTime(0, 0))
            # 重新连接信号
            self.te_start_time.timeChanged.connect(self.validate_start_time)

    def validate_end_time(self, time):
        """验证结束时间"""
        # 移除8-23点限制，只检查是否早于或等于开始时间
        if time <= self.te_start_time.time():
            QMessageBox.warning(
                self,
                "时间设置错误",
                "监控结束时间不能早于或等于开始时间！\n已自动调整为23:59"
            )
            # 临时断开信号，避免递归调用
            self.te_end_time.timeChanged.disconnect()
            self.te_end_time.setTime(QTime(23, 59))
            # 重新连接信号
            self.te_end_time.timeChanged.connect(self.validate_end_time)

    def start_monitor(self):
        """开始监控"""
        # 检查是否已经在运行
        if self.monitor_core.running or self.monitor_core.isRunning():
            QMessageBox.information(
                self, "监控已运行",
                "监控已经在运行中！\n\n"
                "如需重新启动，请先停止当前监控。"
            )
            return

        # 检查关键词配置
        keywords = []
        if self.cb_gaoji.isChecked():
            keywords.append("高级中学")
        if self.cb_zhong.isChecked():
            keywords.append("中学")

        if not keywords:
            QMessageBox.warning(self, "配置错误", "请至少选择一个监控关键词！")
            return

        # 重新加载配置，确保获取最新状态
        self.config_manager.load_config()

        # 检查代理配置
        proxy_enabled = self.config_manager.is_proxy_enabled()
        kuaidaili_enabled = self.config_manager.is_kuaidaili_enabled()

        # 调试信息
        print(f"🔍 启动监控前检查:")
        print(f"   - 代理启用: {proxy_enabled}")
        print(f"   - 快代理启用: {kuaidaili_enabled}")
        print(f"   - Secret ID: {self.config_manager.get_kuaidaili_secret_id()}")
        print(f"   - Signature: {self.config_manager.get_kuaidaili_signature()}")

        if not proxy_enabled:
            reply = QMessageBox.critical(
                self, "代理未启用",
                "❌ 无法启动监控：代理未启用！\n\n"
                "本软件需启用代理才能正常工作。\n"
                "是否现在打开代理配置？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply == QMessageBox.Yes:
                self.show_proxy_dialog()
            return

        if not kuaidaili_enabled:
            reply = QMessageBox.critical(
                self, "快代理未配置",
                "❌ 无法启动监控：快代理API未配置！\n\n"
                "请配置快代理的Secret ID和Signature。\n"
                "是否现在打开代理配置？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply == QMessageBox.Yes:
                self.show_proxy_dialog()
            return

        # 保存配置
        self.save_config()

        # 启动监控
        if self.monitor_core.start_monitor(keywords):
            self.btn_start.setEnabled(False)
            self.btn_stop.setEnabled(True)
            self.add_log("监控已启动")
            self.statusBar().showMessage("监控运行中...")
        else:
            QMessageBox.critical(
                self, "启动失败",
                "监控启动失败！\n\n"
                "可能的原因：\n"
                "• 快代理API连接失败\n"
                "• 代理配置错误\n"
                "• 网络连接问题\n\n"
                "请检查日志信息或重新配置代理。"
            )

    def stop_monitor(self):
        """停止监控"""
        self.monitor_core.stop_monitor()
        self.btn_start.setEnabled(True)
        self.btn_stop.setEnabled(False)
        self.add_log("监控已停止")
        self.statusBar().showMessage("监控已停止")



    def test_webhook(self):
        """测试webhook"""
        webhook_url = self.le_webhook.text().strip()
        if not webhook_url:
            QMessageBox.warning(self, "配置错误", "请先输入webhook地址！")
            return

        from core.notification import NotificationManager
        notification = NotificationManager(webhook_url)
        success, msg = notification.test_webhook()

        if success:
            QMessageBox.information(self, "测试成功", "Webhook连接测试成功！")
        else:
            QMessageBox.warning(self, "测试失败", f"Webhook连接测试失败：\n{msg}")

    def show_proxy_dialog(self):
        """显示代理配置对话框"""
        dialog = ProxyDialog(self)
        # 让代理配置对话框使用同一个ConfigManager实例
        dialog.config_manager = self.config_manager
        dialog.kuaidaili_manager.config_manager = self.config_manager
        dialog.load_config()  # 重新加载配置
        dialog.exec_()

    def clear_log(self):
        """清空日志"""
        self.te_log.clear()
        self.add_log("日志已清空")

    def add_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.te_log.append(formatted_message)

        # 自动滚动到底部
        cursor = self.te_log.textCursor()
        cursor.movePosition(cursor.End)
        self.te_log.setTextCursor(cursor)

    def update_status(self, status):
        """更新状态"""
        self.lbl_status.setText(f"状态: {status}")

        if status == "运行中":
            self.lbl_status.setStyleSheet("""
                QLabel {
                    color: #28a745;
                    font-weight: bold;
                    padding: 8px 12px;
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                }
            """)
        elif status == "已停止":
            self.lbl_status.setStyleSheet("""
                QLabel {
                    color: #dc3545;
                    font-weight: bold;
                    padding: 8px 12px;
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                }
            """)
        else:
            self.lbl_status.setStyleSheet("""
                QLabel {
                    color: #6c757d;
                    font-weight: bold;
                    padding: 8px 12px;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                }
            """)

    def handle_new_data(self, keyword, new_data):
        """处理新增数据"""
        count = len(new_data)
        self.add_log(f"🎉 {keyword} 发现 {count} 条新增数据！")

        # 显示系统托盘通知
        if hasattr(self, 'tray_icon'):
            self.tray_icon.showMessage(
                "发现新增数据",
                f"{keyword} 发现 {count} 条新增数据",
                QSystemTrayIcon.Information,
                3000
            )

    def tray_icon_activated(self, reason):
        """托盘图标激活"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show_window()

    def show_window(self):
        """显示窗口"""
        self.show()
        self.raise_()
        self.activateWindow()

    def quit_application(self):
        """退出应用程序"""
        # 停止监控
        if self.monitor_core.running:
            self.monitor_core.stop_monitor()

        # 清理资源
        if hasattr(self.monitor_core, 'resource_manager'):
            self.monitor_core.resource_manager.cleanup()

        # 清理JavaScript执行器
        if hasattr(self.monitor_core, 'cleanup'):
            self.monitor_core.cleanup()

        # 退出程序
        from PyQt5.QtWidgets import QApplication
        QApplication.quit()

    def closeEvent(self, event):
        """重写关闭事件，最小化到托盘而不是关闭"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.hide()
            self.tray_icon.showMessage(
                "程序已最小化",
                "程序已最小化到系统托盘，双击托盘图标可重新显示",
                QSystemTrayIcon.Information,
                2000
            )
            event.ignore()
        else:
            # 如果没有托盘支持，则直接退出
            self.quit_application()
            event.accept()
