"""
配置管理模块
"""
import configparser
import os
from datetime import datetime


class ConfigManager:
    """配置管理类"""
    
    def __init__(self, config_file="config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._save_lock = False  # 防止配置保存冲突
        self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self._create_default_config()
            
    def _create_default_config(self):
        """创建默认配置"""
        # Monitor配置
        self.config.add_section('Monitor')
        self.config.set('Monitor', 'keywords', '高级中学,中学')
        # 注意：监控间隔已固定为5-6分钟，不在配置文件中设置
        # 这样可以防止客户修改间隔时间，确保系统稳定性
        self.config.set('Monitor', 'start_time', '00:00')
        self.config.set('Monitor', 'end_time', '23:59')
        self.config.set('Monitor', 'enabled', 'true')
        
        # Database配置
        self.config.add_section('Database')
        self.config.set('Database', 'last_check_time', '')
        
        # Notification配置
        self.config.add_section('Notification')
        self.config.set('Notification', 'webhook_url', '')
        self.config.set('Notification', 'enabled', 'true')
        
        # Proxy配置
        self.config.add_section('Proxy')
        self.config.set('Proxy', 'enabled', 'false')
        self.config.set('Proxy', 'max_consecutive_failures', '3')  # 连续失败3次后暂时禁用

        # 快代理配置
        self.config.add_section('Kuaidaili')
        self.config.set('Kuaidaili', 'secret_id', '')
        self.config.set('Kuaidaili', 'signature', '')
        self.config.set('Kuaidaili', 'proxy_count', '10')  # 每次获取的代理数量
        
        # Logging配置
        self.config.add_section('Logging')
        self.config.set('Logging', 'level', 'INFO')
        self.config.set('Logging', 'max_size', '10MB')
        self.config.set('Logging', 'backup_count', '5')
        
        self.save_config()
        
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
            
    def get_keywords(self):
        """获取监控关键词列表"""
        keywords_str = self.config.get('Monitor', 'keywords', fallback='高级中学,中学')
        return [k.strip() for k in keywords_str.split(',') if k.strip()]
        
    def set_keywords(self, keywords):
        """设置监控关键词"""
        keywords_str = ','.join(keywords)
        self.config.set('Monitor', 'keywords', keywords_str)
        self.save_config()
        
    def get_monitor_interval(self):
        """获取监控间隔（分钟）- 固定值，不允许客户修改"""
        # 硬编码间隔时间，确保稳定性和安全性
        # 这些值经过优化，既能避免被目标网站识别，又能保证监控效果
        min_interval = 5.0  # 固定最小间隔5分钟
        max_interval = 6.0  # 固定最大间隔6分钟

        # 检查配置文件中是否有客户尝试修改间隔的行为
        if self.config.has_option('Monitor', 'interval_min') or self.config.has_option('Monitor', 'interval_max'):
            print("⚠️ 检测到配置文件中存在间隔设置")
            print("   系统将使用固定间隔(5-6分钟)以确保稳定性和安全性")
            print("   请勿修改监控间隔，以免影响系统正常运行")

        return min_interval, max_interval
        
    def set_monitor_interval(self, min_interval=None, max_interval=None):
        """设置监控间隔 - 已禁用，使用固定间隔"""
        # 此方法已禁用，间隔时间由系统固定，不允许修改
        # 参数被忽略，仅用于兼容性
        _ = min_interval  # 忽略参数
        _ = max_interval  # 忽略参数

        print("⚠️ 监控间隔已固定为5-6分钟，无法修改以确保系统稳定性")
        print("   固定间隔设计是为了：")
        print("   1. 避免过于频繁的请求被目标网站识别")
        print("   2. 防止IP被封禁")
        print("   3. 确保系统稳定运行")
        # 不执行任何实际的设置操作
        return False
        
    def get_monitor_time(self):
        """获取监控时间段"""
        start_time = self.config.get('Monitor', 'start_time', fallback='00:00')
        end_time = self.config.get('Monitor', 'end_time', fallback='23:59')
        return start_time, end_time
        
    def set_monitor_time(self, start_time, end_time):
        """设置监控时间段"""
        self.config.set('Monitor', 'start_time', start_time)
        self.config.set('Monitor', 'end_time', end_time)
        self.save_config()
        
    def get_webhook_url(self):
        """获取webhook地址"""
        return self.config.get('Notification', 'webhook_url', fallback='')
        
    def set_webhook_url(self, url):
        """设置webhook地址"""
        self.config.set('Notification', 'webhook_url', url)
        self.save_config()
        
    def is_notification_enabled(self):
        """是否启用通知"""
        return self.config.getboolean('Notification', 'enabled', fallback=True)
        
    def set_notification_enabled(self, enabled):
        """设置通知启用状态"""
        self.config.set('Notification', 'enabled', str(enabled).lower())
        self.save_config()
        

        
    def is_proxy_enabled(self):
        """是否启用代理"""
        return self.config.getboolean('Proxy', 'enabled', fallback=False)
        
    def set_proxy_enabled(self, enabled):
        """设置代理启用状态"""
        self.config.set('Proxy', 'enabled', str(enabled).lower())
        self.save_config()

    def get_proxy_max_consecutive_failures(self):
        """获取代理最大连续失败次数"""
        return self.config.getint('Proxy', 'max_consecutive_failures', fallback=3)

    def set_proxy_max_consecutive_failures(self, count):
        """设置代理最大连续失败次数"""
        self.config.set('Proxy', 'max_consecutive_failures', str(count))
        self.save_config()

    def is_kuaidaili_enabled(self):
        """是否启用快代理API（有凭证就启用）"""
        secret_id = self.get_kuaidaili_secret_id()
        signature = self.get_kuaidaili_signature()
        return bool(secret_id and signature)

    def set_kuaidaili_enabled(self, enabled=None):
        """设置快代理API启用状态（兼容性方法，实际由凭证决定）"""
        # 这个方法保留是为了兼容性，实际启用状态由凭证决定
        # enabled参数被忽略，实际状态由API凭证决定
        _ = enabled  # 忽略参数
        pass

    def get_kuaidaili_secret_id(self):
        """获取快代理secret_id"""
        return self.config.get('Kuaidaili', 'secret_id', fallback='')

    def set_kuaidaili_secret_id(self, secret_id):
        """设置快代理secret_id"""
        if not self.config.has_section('Kuaidaili'):
            self.config.add_section('Kuaidaili')
        self.config.set('Kuaidaili', 'secret_id', secret_id)
        self.save_config()

    def get_kuaidaili_signature(self):
        """获取快代理signature"""
        return self.config.get('Kuaidaili', 'signature', fallback='')

    def set_kuaidaili_signature(self, signature):
        """设置快代理signature"""
        if not self.config.has_section('Kuaidaili'):
            self.config.add_section('Kuaidaili')
        self.config.set('Kuaidaili', 'signature', signature)
        self.save_config()

    def get_kuaidaili_proxy_count(self):
        """获取快代理每次获取的代理数量"""
        return self.config.getint('Kuaidaili', 'proxy_count', fallback=10)

    def set_kuaidaili_proxy_count(self, count):
        """设置快代理每次获取的代理数量"""
        if not self.config.has_section('Kuaidaili'):
            self.config.add_section('Kuaidaili')
        self.config.set('Kuaidaili', 'proxy_count', str(count))
        self.save_config()
        
    # 已删除 get_last_count 和 set_last_count 方法
    # 现在完全依赖数据库记录数量，不再在配置文件中重复存储
        
    def get_last_check_time(self):
        """获取上次检查时间"""
        return self.config.get('Database', 'last_check_time', fallback='')
        
    def set_last_check_time(self, check_time=None):
        """设置上次检查时间"""
        if check_time is None:
            check_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.config.set('Database', 'last_check_time', check_time)
        self.save_config()
