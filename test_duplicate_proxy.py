#!/usr/bin/env python3
"""
测试重复获取代理问题的修复
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.config import ConfigManager
from core.monitor import MonitorCore
from utils.resource_manager import ResourceManager

def test_duplicate_proxy_fix():
    """测试重复获取代理问题的修复"""
    print("🧪 测试重复获取代理问题的修复...")
    print("=" * 60)
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 检查代理配置
    proxy_enabled = config_manager.is_proxy_enabled()
    kuaidaili_enabled = config_manager.is_kuaidaili_enabled()
    
    print(f"📋 代理配置状态:")
    print(f"   代理启用: {proxy_enabled}")
    print(f"   快代理API启用: {kuaidaili_enabled}")
    
    if not proxy_enabled or not kuaidaili_enabled:
        print("⚠️ 代理或快代理API未启用，无法测试")
        return
    
    # 初始化资源管理器和监控核心
    resource_manager = ResourceManager()
    monitor_core = MonitorCore(resource_manager)
    
    print("\n🔍 测试监控核心的代理准备逻辑...")
    
    # 模拟监控启动过程
    print("\n1️⃣ 第一次调用 _prepare_proxy_for_cycle:")
    monitor_core.proxy_prepared = False  # 重置标志
    monitor_core._prepare_proxy_for_cycle()
    
    print(f"   代理准备标志: {getattr(monitor_core, 'proxy_prepared', False)}")
    if hasattr(monitor_core.proxy_manager, 'current_proxy'):
        print(f"   当前代理: {monitor_core.proxy_manager.current_proxy}")
    
    print("\n2️⃣ 第二次调用 _prepare_proxy_for_cycle:")
    monitor_core._prepare_proxy_for_cycle()
    
    print("\n3️⃣ 第三次调用 _prepare_proxy_for_cycle:")
    monitor_core._prepare_proxy_for_cycle()
    
    print(f"\n📊 测试结果:")
    print(f"   代理准备标志: {getattr(monitor_core, 'proxy_prepared', False)}")
    if hasattr(monitor_core.proxy_manager, 'current_proxy'):
        print(f"   最终代理: {monitor_core.proxy_manager.current_proxy}")
    
    print("\n✅ 如果只看到一次代理获取过程，说明修复成功")

def test_api_connection():
    """测试API连接（不应该显示代理获取过程）"""
    print("\n" + "=" * 60)
    print("🧪 测试API连接...")
    
    config_manager = ConfigManager()
    
    from core.kuaidaili_manager import KuaidailiManager
    kuaidaili_manager = KuaidailiManager(config_manager)
    
    print("🔍 测试API连接（应该不显示详细的代理获取过程）...")
    success, message = kuaidaili_manager.test_api_connection()
    
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")

if __name__ == "__main__":
    try:
        test_duplicate_proxy_fix()
        test_api_connection()
        print("\n🎉 重复获取代理问题修复测试完成！")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
