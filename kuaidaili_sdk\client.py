"""
快代理API客户端
"""
import requests
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from .config import KuaidailiConfig
from .validator import ProxyValidator
from .exceptions import APIException, ProxyException


class KuaidailiClient:
    """快代理API客户端"""
    
    def __init__(self, secret_id: Optional[str] = None, 
                 signature: Optional[str] = None,
                 config: Optional[KuaidailiConfig] = None,
                 timeout: int = 10):
        """
        初始化客户端
        
        Args:
            secret_id: 快代理Secret ID
            signature: 快代理Signature  
            config: 配置对象
            timeout: 请求超时时间
        """
        if config:
            self.config = config
        elif secret_id and signature:
            self.config = KuaidailiConfig(secret_id=secret_id, signature=signature)
        else:
            raise APIException("必须提供 secret_id 和 signature 或 config 对象")
        
        self.api_url = "https://dps.kdlapi.com/api/getdps"
        self.timeout = timeout
        self.validator = ProxyValidator()
        
        # 缓存相关
        self.proxy_cache = []
        self.cache_expire_time = None
        self.cache_duration = 300  # 5分钟缓存
    
    @classmethod
    def from_config_file(cls, config_file: str) -> 'KuaidailiClient':
        """从配置文件创建客户端"""
        config = KuaidailiConfig()
        config.load_from_file(config_file)
        return cls(config=config)
    
    @classmethod
    def from_env(cls) -> 'KuaidailiClient':
        """从环境变量创建客户端"""
        config = KuaidailiConfig.from_env()
        return cls(config=config)
    
    def test_connection(self) -> tuple:
        """
        测试API连接
        
        Returns:
            tuple: (是否成功, 消息)
        """
        try:
            # 尝试获取1个代理来测试连接
            proxies = self.fetch_proxies(num=1)
            if proxies:
                return True, "API连接正常"
            else:
                return False, "API连接失败，无法获取代理"
        except Exception as e:
            return False, f"API连接异常: {str(e)}"
    
    def fetch_proxies(self, num: int = 10) -> List[str]:
        """
        从快代理API获取代理
        
        Args:
            num: 获取代理数量
            
        Returns:
            List[str]: 代理列表
        """
        secret_id, signature = self.config.get_api_credentials()
        
        params = {
            "secret_id": secret_id,
            "signature": signature,
            "num": num,
        }
        
        try:
            print(f"🔄 正在从快代理API获取 {num} 个代理...")
            response = requests.get(self.api_url, params=params, timeout=self.timeout)
            
            if response.status_code == 200:
                proxy_text = response.text.strip()
                if proxy_text:
                    # 检查是否是错误信息
                    if any(keyword in proxy_text.lower() for keyword in ["error", "fail", "invalid"]):
                        raise APIException(f"快代理API返回错误: {proxy_text}")
                    
                    # 解析代理列表
                    proxies = [line.strip() for line in proxy_text.split('\n') if line.strip()]
                    
                    if proxies:
                        print(f"✅ 成功获取 {len(proxies)} 个代理")
                        return proxies
                    else:
                        raise APIException("快代理API返回空数据")
                else:
                    raise APIException("快代理API返回空响应")
            else:
                raise APIException(f"快代理API请求失败: HTTP {response.status_code}", 
                                 response.status_code, response.text)
                
        except requests.RequestException as e:
            raise APIException(f"快代理API请求异常: {e}")
        except Exception as e:
            raise APIException(f"获取快代理时发生错误: {e}")
    
    def get_proxy(self, validate: bool = True) -> Optional[str]:
        """
        获取单个代理
        
        Args:
            validate: 是否验证代理可用性
            
        Returns:
            Optional[str]: 代理地址，如果获取失败返回None
        """
        try:
            proxies = self.fetch_proxies(num=1)
            if not proxies:
                return None
            
            proxy = proxies[0]
            
            if validate:
                if self.validator.validate_proxy(proxy):
                    return proxy
                else:
                    print(f"❌ 代理验证失败: {proxy}")
                    return None
            else:
                return proxy
                
        except Exception as e:
            print(f"❌ 获取代理失败: {e}")
            return None
    
    def get_verified_proxy(self, max_attempts: int = 5) -> Optional[str]:
        """
        获取经过验证的可用代理
        
        Args:
            max_attempts: 最大尝试次数
            
        Returns:
            Optional[str]: 验证通过的代理地址
        """
        for attempt in range(max_attempts):
            print(f"🔄 获取验证代理尝试 {attempt + 1}/{max_attempts}")
            
            proxy = self.get_proxy(validate=True)
            if proxy:
                print(f"✅ 第 {attempt + 1} 次尝试成功获取验证代理")
                return proxy
            
            # 失败后等待一段时间再重试
            if attempt < max_attempts - 1:
                wait_time = min((attempt + 1) * 2, 10)
                print(f"⏳ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        print(f"❌ 尝试了 {max_attempts} 次，未能获取可用代理")
        return None

    def get_proxy_list(self, num: int = 10, validate: bool = False) -> List[str]:
        """
        获取代理列表

        Args:
            num: 获取代理数量
            validate: 是否验证代理可用性

        Returns:
            List[str]: 代理列表
        """
        try:
            proxies = self.fetch_proxies(num=num)

            if validate and proxies:
                print(f"🔄 验证 {len(proxies)} 个代理...")
                valid_proxies = self.validator.get_valid_proxies(proxies)
                print(f"✅ 验证完成，{len(valid_proxies)}/{len(proxies)} 个代理可用")
                return valid_proxies

            return proxies

        except Exception as e:
            print(f"❌ 获取代理列表失败: {e}")
            return []

    def get_cached_proxies(self, num: int = 10, force_refresh: bool = False) -> List[str]:
        """
        获取缓存的代理（如果缓存过期则重新获取）

        Args:
            num: 获取代理数量
            force_refresh: 是否强制刷新缓存

        Returns:
            List[str]: 代理列表
        """
        current_time = datetime.now()

        # 检查是否需要刷新缓存
        if (force_refresh or
            not self.proxy_cache or
            not self.cache_expire_time or
            current_time > self.cache_expire_time):

            print("🔄 代理缓存已过期或为空，重新获取...")
            new_proxies = self.fetch_proxies(num)

            if new_proxies:
                self.proxy_cache = new_proxies
                self.cache_expire_time = current_time + timedelta(seconds=self.cache_duration)
                print(f"✅ 代理缓存已更新，缓存 {len(self.proxy_cache)} 个代理")
            else:
                print("❌ 无法获取新代理，使用现有缓存")

        return self.proxy_cache.copy()

    def validate_proxy(self, proxy: str) -> bool:
        """
        验证代理是否可用

        Args:
            proxy: 代理地址

        Returns:
            bool: 是否可用
        """
        return self.validator.validate_proxy(proxy)

    def get_proxy_config(self, proxy: str) -> Dict[str, str]:
        """
        获取代理配置字典

        Args:
            proxy: 代理地址

        Returns:
            Dict[str, str]: 代理配置字典，可直接用于requests
        """
        if '://' not in proxy:
            proxy = f'http://{proxy}'

        return {
            'http': proxy,
            'https': proxy
        }
