"""
中国社会组织监控工具 - 主程序入口
"""
import sys
import os

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 首先导入subprocess拦截器，确保所有子进程都隐藏窗口
from utils.subprocess_interceptor import apply_global_interceptor  # 这会自动应用全局拦截器

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from ui.main_window import MainWindow
from utils.single_instance import SingleInstance
from utils.logger import LogManager


def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # 关闭窗口不退出程序
    
    # 检查单实例
    single_instance = SingleInstance()
    if single_instance.is_running():
        single_instance.show_already_running_message()
        sys.exit(1)
        
    try:
        # 创建日志管理器
        log_manager = LogManager()
        log_manager.info("程序启动")
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        # 运行应用程序
        result = app.exec_()
        
        # 清理资源
        log_manager.info("程序退出")
        single_instance.cleanup()
        
        return result
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        single_instance.cleanup()
        return 1


if __name__ == '__main__':
    sys.exit(main())
