# 首先导入subprocess拦截器，确保所有子进程都隐藏窗口
try:
    from utils.subprocess_interceptor import apply_global_interceptor  # 这会自动应用全局拦截器
except ImportError:
    pass  # 如果在独立运行时没有utils模块，忽略错误

import requests
import base64
import ddddocr
import execjs
import pprint
import time
import random
import sys

# 控制是否显示调试信息
SHOW_DEBUG_INFO = False

def get_image():

    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Connection': 'keep-alive',
        # 'Cookie': 'https_waf_cookie=18378829-4d28-4d841c2ee84652d79b342b3c0ad8727b7f4b; SF_cookie_129=12016923',
        'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/newlist',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Microsoft Edge";v="126"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    response = requests.get('https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slideCaptcha',headers=headers,).json()
    a = response['result']['a']
    b = response['result']['b']
    cutImage = response['result']['c']['cutImage']
    oriImage = response['result']['c']['oriImage']
    with open('cut.png', 'wb') as f1:
        f1.write(base64.b64decode(cutImage))
    with open('ori.png', 'wb') as f2:
        f2.write(base64.b64decode(oriImage))
    return a,b

def get_xcode():
    """优化的验证码识别函数"""
    try:
        # 创建ddddocr实例，优化参数
        det = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)

        # 读取图片文件
        with open('cut.png', 'rb') as f:
            target_bytes = f.read()
        with open('ori.png', 'rb') as f:
            background_bytes = f.read()

        # 使用滑块匹配，尝试多种参数组合提高识别率
        try:
            # 首先尝试默认参数
            res = det.slide_match(target_bytes, background_bytes, simple_target=True)['target'][0]
            return res
        except Exception:
            # 如果默认参数失败，尝试不使用simple_target
            try:
                res = det.slide_match(target_bytes, background_bytes, simple_target=False)['target'][0]
                return res
            except Exception:
                # 最后的备选方案，返回一个随机值
                import random
                return random.randint(50, 200)

    except Exception as e:
        print(f"验证码识别出错: {e}")
        # 返回一个随机值作为备选
        import random
        return random.randint(50, 200)
 
def get_params(a,b,xcode):
    """获取加密参数 - 使用持久化JavaScript执行器"""
    from utils.js_executor import get_js_executor

    js_executor = get_js_executor()

    # 如果执行器未初始化，使用execjs作为备选
    if not hasattr(js_executor, '_initialized') or not js_executor._initialized:
        print("⚠️ JavaScript执行器未初始化，使用execjs备选方案")
        return _get_params_fallback(a, b, xcode)

    # 使用持久化执行器
    result, error = js_executor.get_params(a, b, xcode)

    if error:
        print(f"⚠️ JavaScript执行器出错: {error}，使用execjs备选方案")
        return _get_params_fallback(a, b, xcode)

    return result

def _get_params_fallback(a, b, xcode):
    """备选方案：使用execjs"""
    import subprocess
    import os

    # 保存原始的subprocess.Popen
    original_popen = subprocess.Popen

    def hidden_popen(*args, **kwargs):
        # 在Windows上隐藏窗口
        if os.name == 'nt':
            if 'startupinfo' not in kwargs:
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                kwargs['startupinfo'] = startupinfo
            if 'creationflags' not in kwargs:
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
        return original_popen(*args, **kwargs)

    # 临时替换subprocess.Popen
    subprocess.Popen = hidden_popen

    try:
        # 尝试多个可能的JavaScript文件路径
        js_file_paths = [
            'main.js',  # 当前目录
            os.path.join(os.path.dirname(__file__), 'main.js'),  # 脚本目录
        ]

        # 如果有资源管理器，尝试使用其提供的路径
        try:
            from utils.resource_manager import ResourceManager
            resource_manager = ResourceManager()
            if resource_manager.is_initialized():
                js_file_paths.insert(0, resource_manager.get_js_file_path())
        except:
            pass

        js_code = None
        for js_path in js_file_paths:
            if js_path and os.path.exists(js_path):
                print(f"使用JavaScript文件: {js_path}")
                js_code = execjs.compile(open(js_path,'r',encoding='utf-8').read())
                break

        if js_code is None:
            raise Exception("未找到可用的JavaScript文件")

        result = js_code.call('get_params',a,b,xcode)
        return result
    finally:
        # 恢复原始的subprocess.Popen
        subprocess.Popen = original_popen
 
def get_check(code):

    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Connection': 'keep-alive',
        # 'Cookie': 'https_waf_cookie=18378829-4d28-4d841c2ee84652d79b342b3c0ad8727b7f4b; SF_cookie_129=12016923',
        'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/newlist',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Microsoft Edge";v="126"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    params = {
        'a': code['a'],
        'b': code['b'],
        'c': code['c'],
    }
    response = requests.get('https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slide_captcha_check',params=params,headers=headers,
    ).json()
    
    # 验证失败后添加随机延时0.5-2秒
    if response.get('msg') != 'success':
        delay = random.uniform(0.5, 2)
        time.sleep(delay)
    
    return response

def get_data(code):
  
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json;charset=UTF-8',
        # 'Cookie': 'https_waf_cookie=18378829-4d28-4d841c2ee84652d79b342b3c0ad8727b7f4b; SF_cookie_129=12016923',
        'Origin': 'https://xxgs.chinanpo.mca.gov.cn',
        'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/newlist',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Microsoft Edge";v="126"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    json_data = {
        'pageNo': 1,
        'pageSize': 10,
        'paramsValue': '高级中学',
        'ssfw': '1',
        'aaae0127': '',
        'xyzk': '',
        'aaae0129': '',
        'aaae0105': '2',
        'aaae0123': '1',
        'aaae0114': '',
        'aae15having': '',
        'aaae0145': '',
        'aaae0110': '',
        'aaae0137': '',
        'aaae0149': '',
        'aaae0136': '',
        'aaae0139': '',
        'a': code['a'],
        'b': code['b'],
        'c': code['c'],
    }
    response = requests.post(
        'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/biz/ma/shzzgsxt/a/gridQuery.html',
        headers=headers,
        json=json_data,
    ).json()
    
    # 检查返回结果是否包含有效数据
    if response.get('result') is None:
        return None, 0
    
    data = response['result']['data']
    total_count = response['result']['totalCount']  # 获取总记录数
    
    return data, total_count

def try_with_retry(max_retries=5):
    """
    使用重试机制尝试获取数据
    
    参数:
    max_retries: 最大重试次数
    
    返回:
    成功获取的数据，如果所有尝试都失败则返回None
    """
    success_attempt = 0  # 记录成功的尝试次数
    
    for attempt in range(1, max_retries + 1):
        # 实时输出当前尝试次数
        print(f"验证码尝试 {attempt}/{max_retries}...", end="", flush=True)
        
        try:
            # 获取验证码图片
            a, b = get_image()
            
            # 识别验证码
            xcode = get_xcode()
            
            # 调整xcode值，可以尝试微调识别结果
            if attempt > 1:
                # 每次重试时微调xcode值，可能会提高成功率
                adjustment = random.randint(-5, 5)
                xcode = max(0, xcode + adjustment)
            
            # 获取加密参数
            code = get_params(a, b, xcode)
            
            # 验证验证码
            check_result = get_check(code)
            
            # 如果验证成功，获取数据
            if check_result.get('msg') == 'success':
                print(" 成功！", flush=True)
                success_attempt = attempt  # 记录成功的尝试次数
                
                result = get_data(code)
                if result[0] is not None:
                    data, total_count = result
                    return data, success_attempt, total_count
                else:
                    print("验证成功但获取数据失败，继续尝试...", flush=True)
            else:
                print(" 失败", flush=True)
            
            # 随机等待一段时间，避免请求过于频繁
            delay = random.uniform(0.5, 2)
            time.sleep(delay)
            
        except Exception as e:
            print(f" 错误: {str(e)[:50]}...", flush=True)
            
            delay = random.uniform(0.5, 2)
            time.sleep(delay)
    
    print(f"已达到最大重试次数 {max_retries}，无法获取数据。", flush=True)
    return None, 0, 0

def print_results(data):
    """打印查询结果"""
    if not data:
        print("未获取到数据")
        return
    
    # 直接打印数据，不显示处理进度
    for item in data:
        org_name = item['aaae0103'].strip()
        org_code = item['aaae0102'].strip()
        valid_date = item['aaae0123'].strip()
        person = item['aaae0113'].strip()
        
        # 限制组织名称长度，避免显示过长
        if len(org_name) > 28:
            org_name = org_name[:25] + "..."
            
        print(f"{org_name} | {org_code} | {person} | {valid_date}")
        print("--------")  # 每条数据后面都添加分隔线

if __name__ == '__main__':
    print("===== 中国社会组织政务服务平台数据爬取 =====")
    
    # 使用重试机制获取数据
    result = try_with_retry(max_retries=5)
    
    if isinstance(result, tuple) and len(result) == 3:
        data, success_attempt, total_count = result
        if data:
            print(f"\n共获取到 {total_count} 条记录，当前显示 {len(data)} 条")
            print_results(data)
            print(f"爬取完成，共有 {total_count} 条记录")
            print(f"本次爬取通过第 {success_attempt} 次验证码尝试成功获取数据")
        else:
            print("获取数据失败")
    else:
        print("获取数据失败")