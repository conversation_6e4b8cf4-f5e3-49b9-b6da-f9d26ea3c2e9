"""
快代理配置对话框
只支持快代理API，移除手动代理配置功能
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit,
                             QPushButton, QLabel, QCheckBox, QMessageBox,
                             QProgressBar, QWidget, QGroupBox, QLineEdit, 
                             QSpinBox, QFormLayout)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

from utils.config import ConfigManager
from core.kuaidaili_manager import KuaidailiManager


class ProxyDialog(QDialog):
    """快代理配置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = ConfigManager()
        self.kuaidaili_manager = KuaidailiManager(self.config_manager)
        self.init_ui()
        self.load_config()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("快代理配置")
        self.setModal(True)
        self.resize(600, 500)

        # 创建主布局
        layout = QVBoxLayout(self)

        # 重要提示
        warning_label = QLabel("⚠️ 重要提示：建议启用代理再开始监控！")
        warning_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Bold))
        warning_label.setStyleSheet("""
            QLabel {
                color: #dc3545;
                background-color: #f8d7da;
                border: 2px solid #f5c6cb;
                border-radius: 6px;
                padding: 10px;
                margin: 5px 0;
            }
        """)
        layout.addWidget(warning_label)

        # 启用代理复选框
        self.cb_enable_proxy = QCheckBox("启用代理池（必须启用）")
        self.cb_enable_proxy.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        self.cb_enable_proxy.setStyleSheet("""
            QCheckBox {
                color: #dc3545;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.cb_enable_proxy)

        # 快代理配置界面
        kuaidaili_widget = self.create_kuaidaili_widget()
        layout.addWidget(kuaidaili_widget)

        # 按钮区域
        button_layout = QHBoxLayout()
        self.btn_save = QPushButton("保存配置")
        self.btn_cancel = QPushButton("取消")

        button_layout.addStretch()
        button_layout.addWidget(self.btn_save)
        button_layout.addWidget(self.btn_cancel)

        layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 连接信号
        self.connect_signals()

    def create_kuaidaili_widget(self):
        """创建快代理API配置界面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 快代理API配置组
        api_group = QGroupBox("快代理API配置")
        api_layout = QFormLayout(api_group)

        # Secret ID
        self.le_secret_id = QLineEdit()
        self.le_secret_id.setPlaceholderText("请输入快代理的secret_id")
        api_layout.addRow("Secret ID:", self.le_secret_id)

        # Signature
        self.le_signature = QLineEdit()
        self.le_signature.setPlaceholderText("请输入快代理的signature")
        api_layout.addRow("Signature:", self.le_signature)

        # 代理数量
        self.sb_proxy_count = QSpinBox()
        self.sb_proxy_count.setRange(1, 100)
        self.sb_proxy_count.setValue(10)
        api_layout.addRow("每次获取代理数量:", self.sb_proxy_count)

        layout.addWidget(api_group)

        # 快代理操作按钮
        kuaidaili_button_layout = QHBoxLayout()
        self.btn_test_api = QPushButton("测试API连接")
        self.btn_fetch_proxies = QPushButton("获取代理")
        self.btn_clear_cache = QPushButton("清空缓存")

        kuaidaili_button_layout.addWidget(self.btn_test_api)
        kuaidaili_button_layout.addWidget(self.btn_fetch_proxies)
        kuaidaili_button_layout.addWidget(self.btn_clear_cache)
        kuaidaili_button_layout.addStretch()

        layout.addLayout(kuaidaili_button_layout)

        # 快代理状态信息
        status_group = QGroupBox("快代理状态")
        status_layout = QVBoxLayout(status_group)

        self.lbl_api_status = QLabel("API状态: 未测试")
        self.lbl_cache_info = QLabel("缓存信息: 无")
        
        status_layout.addWidget(self.lbl_api_status)
        status_layout.addWidget(self.lbl_cache_info)

        layout.addWidget(status_group)

        # 获取的代理列表
        proxy_group = QGroupBox("获取的代理列表")
        proxy_layout = QVBoxLayout(proxy_group)

        self.te_fetched_proxies = QTextEdit()
        self.te_fetched_proxies.setReadOnly(True)
        self.te_fetched_proxies.setFont(QFont("Consolas", 10))
        self.te_fetched_proxies.setMaximumHeight(150)
        proxy_layout.addWidget(self.te_fetched_proxies)

        layout.addWidget(proxy_group)

        layout.addStretch()
        return widget

    def connect_signals(self):
        """连接信号槽"""
        # 快代理相关
        self.btn_test_api.clicked.connect(self.test_kuaidaili_api)
        self.btn_fetch_proxies.clicked.connect(self.fetch_kuaidaili_proxies)
        self.btn_clear_cache.clicked.connect(self.clear_kuaidaili_cache)
        
        # 通用按钮
        self.btn_save.clicked.connect(self.save_config)
        self.btn_cancel.clicked.connect(self.reject)

    def load_config(self):
        """加载配置"""
        # 加载代理启用状态
        enabled = self.config_manager.is_proxy_enabled()
        print(f"加载代理启用状态: {enabled}")
        self.cb_enable_proxy.setChecked(enabled)

        # 加载快代理配置
        secret_id = self.config_manager.get_kuaidaili_secret_id()
        self.le_secret_id.setText(secret_id)

        signature = self.config_manager.get_kuaidaili_signature()
        self.le_signature.setText(signature)

        proxy_count = self.config_manager.get_kuaidaili_proxy_count()
        self.sb_proxy_count.setValue(proxy_count)

        # 更新快代理状态
        self.update_kuaidaili_status()

    def save_config(self):
        """保存配置"""
        # 检查是否启用代理
        enabled = self.cb_enable_proxy.isChecked()
        if not enabled:
            reply = QMessageBox.warning(
                self, "代理未启用",
                "⚠️ 提示：未启用代理将影响监控运行！\n\n"
                "本软件需使用代理才能正常工作。\n",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        # 检查快代理配置
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()

        if enabled and (not secret_id or not signature):
            QMessageBox.warning(
                self, "配置不完整",
                "❌ 错误：启用代理但未配置快代理API！\n\n"
                "请填写快代理的Secret ID和Signature，\n"
                "或者取消启用代理（但这将无法开始监控）。"
            )
            return

        # 保存代理启用状态
        print(f"保存代理启用状态: {enabled}")
        self.config_manager.set_proxy_enabled(enabled)

        # 保存快代理配置
        self.config_manager.set_kuaidaili_secret_id(secret_id)
        self.config_manager.set_kuaidaili_signature(signature)

        proxy_count = self.sb_proxy_count.value()
        self.config_manager.set_kuaidaili_proxy_count(proxy_count)

        # 自动启用快代理（如果配置了凭证）
        if secret_id and signature:
            self.config_manager.set_kuaidaili_enabled(True)
            if enabled:
                QMessageBox.information(
                    self, "保存成功",
                    f"✅ 快代理配置已保存！\n\n"
                    f"代理启用状态: 已启用\n"
                    f"快代理API: 已配置\n"
                    f"每次获取代理数量: {proxy_count}\n\n"
                    f"现在可以开始监控了！"
                )
            else:
                QMessageBox.warning(
                    self, "配置已保存",
                    f"⚠️ 快代理配置已保存，但代理未启用！\n\n"
                    f"请注意：代理未启用将无法开始监控。"
                )
        else:
            self.config_manager.set_kuaidaili_enabled(False)
            if enabled:
                QMessageBox.warning(
                    self, "配置不完整",
                    f"⚠️ 代理已启用但快代理API未配置！\n\n"
                    f"请配置快代理的Secret ID和Signature才能开始监控。"
                )
            else:
                QMessageBox.information(
                    self, "保存成功",
                    f"代理配置已保存！\n\n"
                    f"代理启用状态: 已禁用\n"
                    f"注意：代理未启用将无法开始监控。"
                )

        self.accept()

    def test_kuaidaili_api(self):
        """测试快代理API连接"""
        # 先保存当前输入的凭证到管理器
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()
        
        if not secret_id or not signature:
            QMessageBox.warning(self, "输入错误", "请先输入Secret ID和Signature！")
            return
        
        # 临时设置凭证进行测试
        self.config_manager.set_kuaidaili_secret_id(secret_id)
        self.config_manager.set_kuaidaili_signature(signature)
        
        # 测试连接
        success, message = self.kuaidaili_manager.test_api_connection()
        
        if success:
            self.lbl_api_status.setText(f"API状态: ✅ 连接成功")
            QMessageBox.information(self, "测试成功", f"快代理API连接成功！\n{message}")
        else:
            self.lbl_api_status.setText(f"API状态: ❌ 连接失败")
            QMessageBox.warning(self, "测试失败", f"快代理API连接失败！\n{message}")

    def fetch_kuaidaili_proxies(self):
        """获取快代理"""
        secret_id = self.le_secret_id.text().strip()
        signature = self.le_signature.text().strip()
        
        if not secret_id or not signature:
            QMessageBox.warning(self, "输入错误", "请先输入Secret ID和Signature！")
            return
        
        # 临时设置凭证
        self.config_manager.set_kuaidaili_secret_id(secret_id)
        self.config_manager.set_kuaidaili_signature(signature)
        
        proxy_count = self.sb_proxy_count.value()
        
        # 获取代理
        proxies = self.kuaidaili_manager.get_cached_proxies(proxy_count, force_refresh=True)
        
        if proxies:
            proxy_text = '\n'.join(proxies)
            self.te_fetched_proxies.setPlainText(proxy_text)
            QMessageBox.information(self, "获取成功", f"成功获取 {len(proxies)} 个代理！")
        else:
            QMessageBox.warning(self, "获取失败", "无法获取代理，请检查API配置和网络连接！")
        
        self.update_kuaidaili_status()

    def clear_kuaidaili_cache(self):
        """清空快代理缓存"""
        self.kuaidaili_manager.clear_cache()
        self.te_fetched_proxies.clear()
        self.update_kuaidaili_status()
        QMessageBox.information(self, "清空成功", "快代理缓存已清空！")

    def update_kuaidaili_status(self):
        """更新快代理状态信息"""
        info = self.kuaidaili_manager.get_proxy_info()
        
        if info['api_configured']:
            api_status = "✅ 已配置" if info['api_configured'] else "❌ 未配置"
        else:
            api_status = "❌ 未配置"
        
        cache_status = f"缓存: {info['cache_count']} 个代理"
        if info['cache_expire_time']:
            if info['is_expired']:
                cache_status += " (已过期)"
            else:
                expire_time = info['cache_expire_time'].strftime("%H:%M:%S")
                cache_status += f" (过期时间: {expire_time})"
        
        self.lbl_api_status.setText(f"API状态: {api_status}")
        self.lbl_cache_info.setText(cache_status)
