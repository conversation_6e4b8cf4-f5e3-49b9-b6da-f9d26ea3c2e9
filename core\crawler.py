"""
爬虫核心模块 - 基于原始main.py改进
"""
import requests
import base64
import execjs
import time
import random
import os
import subprocess

# 可选导入ddddocr，处理NumPy兼容性问题
try:
    # 首先检查NumPy版本兼容性
    import numpy
    numpy_version = numpy.__version__
    if numpy_version.startswith('2.'):
        print(f"警告: 检测到NumPy {numpy_version}，可能与ddddocr不兼容")
        print("建议执行: pip install 'numpy<2' 来修复此问题")
        raise ImportError("NumPy版本不兼容")

    import ddddocr
    # 尝试创建对象来确保完全可用
    test_det = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
    DDDDOCR_AVAILABLE = True
    print("✅ ddddocr导入并初始化成功")
except ImportError as e:
    print(f"警告: ddddocr导入失败 ({e})，验证码识别功能将不可用")
    DDDDOCR_AVAILABLE = False
except Exception as e:
    print(f"警告: ddddocr初始化失败 ({e})，验证码识别功能将不可用")
    DDDDOCR_AVAILABLE = False


class CrawlerCore:
    """爬虫核心类"""
    
    def __init__(self, resource_manager=None, proxy_manager=None, config_manager=None):
        self.resource_manager = resource_manager
        self.proxy_manager = proxy_manager
        self.config_manager = config_manager
        self.js_context = None
        self.session = requests.Session()
        
    def init_js_context(self):
        """初始化JavaScript执行环境"""
        from utils.js_executor import get_js_executor

        # 获取JavaScript执行器
        self.js_executor = get_js_executor()

        # 如果资源管理器可用，使用持久化执行器
        if (self.resource_manager and
            self.resource_manager.is_initialized() and
            self.resource_manager.get_node_path() and
            self.resource_manager.get_js_file_path()):

            node_path = self.resource_manager.get_node_path()
            js_file_path = self.resource_manager.get_js_file_path()

            if self.js_executor.initialize(node_path, js_file_path):
                print("✅ 使用持久化JavaScript执行器")
                self.use_persistent_js = True
                return True
            else:
                print("⚠️ 持久化JavaScript执行器初始化失败，使用execjs备选")

        # 备选方案：使用execjs
        self.use_persistent_js = False
        return self._init_execjs_fallback()

    def _init_execjs_fallback(self):
        """备选方案：初始化execjs"""
        import os

        # 优先使用资源管理器提供的JavaScript文件路径
        js_file_path = None

        if (self.resource_manager and
            self.resource_manager.is_initialized() and
            self.resource_manager.get_js_file_path()):
            js_file_path = self.resource_manager.get_js_file_path()
            print(f"使用资源管理器提供的JavaScript文件: {js_file_path}")
        else:
            # 备选方案：使用当前目录下的main.js文件
            js_file_path = 'main.js'
            print(f"使用当前目录下的JavaScript文件: {js_file_path}")

        if os.path.exists(js_file_path):
            with open(js_file_path, 'r', encoding='utf-8') as f:
                js_code = f.read()
            self.js_context = execjs.compile(js_code)
            print(f"✅ JavaScript执行环境初始化成功: {js_file_path}")
            return True
        else:
            print(f"❌ 未找到JavaScript文件: {js_file_path}")
            return False
        
    def get_image(self):
        """获取验证码图片"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Connection': 'keep-alive',
            'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/newlist',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Microsoft Edge";v="126"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        # 根据配置决定是否使用代理
        proxies = None
        if self.proxy_manager and hasattr(self.proxy_manager, 'proxy_list') and self.proxy_manager.proxy_list:
            proxies = self.proxy_manager.get_proxy()

        # 不使用cookies（与main.py保持一致），但支持代理
        response = requests.get(
            'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slideCaptcha',
            headers=headers,
            proxies=proxies
        ).json()
        
        a = response['result']['a']
        b = response['result']['b']
        cutImage = response['result']['c']['cutImage']
        oriImage = response['result']['c']['oriImage']
        
        # 保存图片到当前目录（与main.py保持一致）
        cut_path = 'cut.png'
        ori_path = 'ori.png'
        
        with open(cut_path, 'wb') as f1:
            f1.write(base64.b64decode(cutImage))
        with open(ori_path, 'wb') as f2:
            f2.write(base64.b64decode(oriImage))
            
        return a, b, cut_path, ori_path

    def get_image_with_proxy(self, proxies):
        """获取验证码图片（使用指定代理）"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/index.html',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        try:
            http_response = requests.get(
                'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slideCaptcha',
                headers=headers,
                proxies=proxies,
                timeout=10
            )

            # 检查HTTP状态码
            if http_response.status_code != 200:
                raise Exception(f"获取验证码图片失败: HTTP {http_response.status_code}")

            # 检查响应内容是否为空
            if not http_response.text.strip():
                raise Exception("获取验证码图片失败: 响应内容为空")

            # 尝试解析JSON
            try:
                response = http_response.json()
            except ValueError as e:
                print(f"响应内容前200字符: {http_response.text[:200]}...")
                raise Exception(f"获取验证码图片失败: JSON解析错误 - {str(e)}")

            # 检查响应结构
            if 'result' not in response:
                raise Exception(f"获取验证码图片失败: 响应中没有result字段 - {response}")

            result = response['result']
            if not all(key in result for key in ['a', 'b', 'c']):
                raise Exception(f"获取验证码图片失败: result字段缺少必要数据 - {result}")

            a = result['a']
            b = result['b']
            c_data = result['c']

            if not all(key in c_data for key in ['cutImage', 'oriImage']):
                raise Exception(f"获取验证码图片失败: c字段缺少图片数据 - {c_data}")

            cutImage = c_data['cutImage']
            oriImage = c_data['oriImage']

            # 保存图片到当前目录
            cut_path = 'cut.png'
            ori_path = 'ori.png'

            try:
                with open(cut_path, 'wb') as f1:
                    f1.write(base64.b64decode(cutImage))
                with open(ori_path, 'wb') as f2:
                    f2.write(base64.b64decode(oriImage))
            except Exception as e:
                raise Exception(f"保存验证码图片失败: {str(e)}")

            return a, b, cut_path, ori_path

        except requests.exceptions.ProxyError as e:
            raise Exception(f"获取验证码图片失败: 代理错误 - {str(e)}")
        except requests.exceptions.Timeout:
            raise Exception("获取验证码图片失败: 请求超时")
        except requests.exceptions.ConnectionError as e:
            raise Exception(f"获取验证码图片失败: 连接错误 - {str(e)}")
        except Exception as e:
            # 如果是我们自己抛出的异常，直接重新抛出
            if str(e).startswith("获取验证码图片失败"):
                raise e
            else:
                raise Exception(f"获取验证码图片失败: 未知错误 - {str(e)}")
        
    def get_xcode(self, cut_path, ori_path):
        """优化的验证码识别函数"""
        if not DDDDOCR_AVAILABLE:
            # 如果ddddocr不可用，返回一个随机值作为备用
            print("警告: ddddocr不可用，使用随机值代替验证码识别")
            return random.randint(50, 200)

        try:
            # 创建ddddocr实例，优化参数
            det = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)

            # 读取图片文件
            with open(cut_path, 'rb') as f:
                target_bytes = f.read()
            with open(ori_path, 'rb') as f:
                background_bytes = f.read()

            # 使用滑块匹配，尝试多种参数组合提高识别率
            try:
                # 首先尝试默认参数
                res = det.slide_match(target_bytes, background_bytes, simple_target=True)['target'][0]
                return res
            except Exception:
                # 如果默认参数失败，尝试不使用simple_target
                try:
                    res = det.slide_match(target_bytes, background_bytes, simple_target=False)['target'][0]
                    return res
                except Exception:
                    # 最后的备选方案，返回一个随机值
                    return random.randint(50, 200)

        except Exception as e:
            print(f"验证码识别出错: {e}")
            # 返回一个随机值作为备选
            return random.randint(50, 200)
        
    def get_params(self, a, b, xcode):
        """获取加密参数"""
        # 确保JavaScript执行环境已初始化
        if not hasattr(self, 'js_executor') and not hasattr(self, 'js_context'):
            if not self.init_js_context():
                raise Exception("JavaScript执行环境初始化失败")

        # 使用持久化执行器
        if hasattr(self, 'use_persistent_js') and self.use_persistent_js:
            result, error = self.js_executor.get_params(a, b, xcode)
            if error:
                print(f"⚠️ 持久化执行器出错: {error}，切换到execjs")
                # 切换到execjs备选方案
                self.use_persistent_js = False
                if not self._init_execjs_fallback():
                    raise Exception("JavaScript执行环境初始化失败")
                return self.js_context.call('get_params', a, b, xcode)
            return result

        # 使用execjs备选方案
        if not hasattr(self, 'js_context') or not self.js_context:
            if not self._init_execjs_fallback():
                raise Exception("JavaScript执行环境初始化失败")

        return self._get_params_with_hidden_window(a, b, xcode)

    def _get_params_with_hidden_window(self, a, b, xcode):
        """使用隐藏窗口的execjs执行"""
        # 配置execjs使用Node.js时隐藏窗口
        original_popen = subprocess.Popen

        def hidden_popen(*args, **kwargs):
            # 在Windows上隐藏窗口
            if os.name == 'nt':
                if 'startupinfo' not in kwargs:
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    kwargs['startupinfo'] = startupinfo
                if 'creationflags' not in kwargs:
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            return original_popen(*args, **kwargs)

        # 临时替换subprocess.Popen
        subprocess.Popen = hidden_popen

        try:
            result = self.js_context.call('get_params', a, b, xcode)
            return result
        finally:
            # 恢复原始的subprocess.Popen
            subprocess.Popen = original_popen
        
    def get_check(self, code):
        """验证验证码"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Connection': 'keep-alive',
            'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/newlist',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Microsoft Edge";v="126"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        params = {
            'a': code['a'],
            'b': code['b'],
            'c': code['c'],
        }

        # 根据配置决定是否使用代理
        proxies = None
        if self.proxy_manager and hasattr(self.proxy_manager, 'proxy_list') and self.proxy_manager.proxy_list:
            proxies = self.proxy_manager.get_proxy()

        response = requests.get(
            'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slide_captcha_check',
            params=params,
            headers=headers,
            proxies=proxies
        ).json()
        
        # 验证失败后添加随机延时0.5-2秒
        if response.get('msg') != 'success':
            delay = random.uniform(0.5, 2)
            time.sleep(delay)
            
        return response

    def get_check_with_proxy(self, code, proxies):
        """验证验证码（使用指定代理）"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/index.html',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        params = {
            'a': code['a'],
            'b': code['b'],
            'c': code['c'],
        }

        try:
            http_response = requests.get(
                'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slide_captcha_check',
                params=params,
                headers=headers,
                proxies=proxies,
                timeout=10
            )

            # 检查HTTP状态码
            if http_response.status_code != 200:
                print(f"❌ 验证码检查失败: HTTP {http_response.status_code}")
                return {'msg': 'fail', 'error': f'HTTP {http_response.status_code}'}

            # 检查响应内容是否为空
            if not http_response.text.strip():
                print("❌ 验证码检查失败: 响应内容为空")
                return {'msg': 'fail', 'error': '响应内容为空'}

            # 尝试解析JSON
            try:
                response = http_response.json()
            except ValueError as e:
                print(f"❌ 验证码检查失败: JSON解析错误 - {str(e)}")
                print(f"响应内容前200字符: {http_response.text[:200]}...")
                return {'msg': 'fail', 'error': f'JSON解析错误: {str(e)}'}

            # 验证失败后添加随机延时0.5-2秒
            if response.get('msg') != 'success':
                delay = random.uniform(0.5, 2)
                time.sleep(delay)

            return response

        except requests.exceptions.ProxyError as e:
            print(f"❌ 验证码检查失败: 代理错误 - {str(e)}")
            return {'msg': 'fail', 'error': f'代理错误: {str(e)}'}
        except requests.exceptions.Timeout:
            print("❌ 验证码检查失败: 请求超时")
            return {'msg': 'fail', 'error': '请求超时'}
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 验证码检查失败: 连接错误 - {str(e)}")
            return {'msg': 'fail', 'error': f'连接错误: {str(e)}'}
        except Exception as e:
            print(f"❌ 验证码检查失败: 未知错误 - {str(e)}")
            return {'msg': 'fail', 'error': f'未知错误: {str(e)}'}

    def get_data(self, code, keyword, page_size=10):
        """获取数据"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://xxgs.chinanpo.mca.gov.cn',
            'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/newlist',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Microsoft Edge";v="126"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        json_data = {
            'pageNo': 1,
            'pageSize': page_size,
            'paramsValue': keyword,
            'ssfw': '1',
            'aaae0127': '',
            'xyzk': '',
            'aaae0129': '',
            'aaae0105': '2',
            'aaae0123': '1',
            'aaae0114': '',
            'aae15having': '',
            'aaae0145': '',
            'aaae0110': '',
            'aaae0137': '',
            'aaae0149': '',
            'aaae0136': '',
            'aaae0139': '',
            'a': code['a'],
            'b': code['b'],
            'c': code['c'],
        }

        # 根据配置决定是否使用代理
        proxies = None
        if self.proxy_manager and hasattr(self.proxy_manager, 'proxy_list') and self.proxy_manager.proxy_list:
            proxies = self.proxy_manager.get_proxy()

        response = requests.post(
            'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/biz/ma/shzzgsxt/a/gridQuery.html',
            headers=headers,
            json=json_data,
            proxies=proxies
        ).json()

        # 检查返回结果是否包含有效数据
        if response.get('result') is None:
            return None, 0

        data = response['result']['data']
        total_count = response['result']['totalCount']  # 获取总记录数

        return data, total_count

    def get_data_with_proxy(self, code, keyword, page_size, proxies):
        """获取数据（使用指定代理）"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://xxgs.chinanpo.mca.gov.cn',
            'Pragma': 'no-cache',
            'Referer': 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/index.html',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        json_data = {
            'pageNo': 1,
            'pageSize': page_size,
            'paramsValue': keyword,
            'ssfw': '1',
            'aaae0127': '',
            'xyzk': '',
            'aaae0129': '',
            'aaae0105': '2',
            'aaae0123': '1',
            'aaae0114': '',
            'aae15having': '',
            'aaae0145': '',
            'aaae0110': '',
            'aaae0137': '',
            'aaae0149': '',
            'aaae0136': '',
            'aaae0139': '',
            'a': code['a'],
            'b': code['b'],
            'c': code['c'],
        }

        try:
            http_response = requests.post(
                'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/biz/ma/shzzgsxt/a/gridQuery.html',
                headers=headers,
                json=json_data,
                proxies=proxies,
                timeout=15
            )

            # 检查HTTP状态码
            if http_response.status_code != 200:
                print(f"❌ 数据获取失败: HTTP {http_response.status_code}")
                return None, 0

            # 检查响应内容类型（放宽检查，因为有些服务器返回text/html但内容是JSON）
            content_type = http_response.headers.get('content-type', '').lower()
            # 只记录Content-Type，不作为失败条件
            if 'application/json' not in content_type:
                print(f"⚠️ 注意: Content-Type为 {content_type}，但尝试解析为JSON")

            # 检查响应内容是否为空
            if not http_response.text.strip():
                print("❌ 数据获取失败: 响应内容为空")
                return None, 0

            # 尝试解析JSON
            try:
                response = http_response.json()
            except ValueError as e:
                print(f"❌ 数据获取失败: JSON解析错误 - {str(e)}")
                print(f"响应内容前200字符: {http_response.text[:200]}...")
                return None, 0

            # 检查返回结果是否包含有效数据
            if response.get('result') is None:
                print(f"❌ 数据获取失败: 响应中没有result字段")
                print(f"响应内容: {response}")
                return None, 0

            data = response['result']['data']
            total_count = response['result']['totalCount']  # 获取总记录数

            return data, total_count

        except requests.exceptions.ProxyError as e:
            print(f"❌ 数据获取失败: 代理错误 - {str(e)}")
            return None, 0
        except requests.exceptions.Timeout:
            print("❌ 数据获取失败: 请求超时")
            return None, 0
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 数据获取失败: 连接错误 - {str(e)}")
            return None, 0
        except Exception as e:
            print(f"❌ 数据获取失败: 未知错误 - {str(e)}")
            return None, 0

    def try_with_retry(self, keyword, page_size=10, max_retries=10, max_proxy_cycles=3):
        """
        使用重试机制尝试获取数据，支持代理重新获取

        参数:
        keyword: 搜索关键词
        page_size: 页面大小
        max_retries: 每个代理的最大重试次数
        max_proxy_cycles: 最大代理重新获取周期数

        返回:
        成功获取的数据，如果所有尝试都失败则返回None
        """
        success_attempt = 0  # 记录成功的尝试次数
        total_attempts = 0  # 总尝试次数

        # 检查代理配置
        proxy_enabled = False
        kuaidaili_enabled = False
        max_proxy_failures = 10

        if self.config_manager:
            self.config_manager.load_config()
            proxy_enabled = self.config_manager.is_proxy_enabled()
            kuaidaili_enabled = self.config_manager.is_kuaidaili_enabled()
            max_proxy_failures = self.config_manager.get_proxy_max_consecutive_failures()

        # 代理重新获取周期循环
        for proxy_cycle in range(max_proxy_cycles):
            proxy_attempt_count = 0  # 当前代理的失败计数
            current_proxy = None

            # 获取代理（优先使用已有代理，避免重复获取）
            if proxy_enabled and kuaidaili_enabled and self.proxy_manager:
                # 首先检查是否已有可用代理
                if (proxy_cycle == 0 and
                    hasattr(self.proxy_manager, 'current_proxy') and
                    self.proxy_manager.current_proxy):
                    # 使用监控核心已经获取的代理
                    current_proxy = {
                        'http': f'http://{self.proxy_manager.current_proxy}',
                        'https': f'http://{self.proxy_manager.current_proxy}'
                    }
                    print(f"✅ 使用已获取的代理: {self.proxy_manager.current_proxy}")
                else:
                    # 只有在没有可用代理或需要重新获取时才获取新代理
                    if proxy_cycle == 0:
                        print("🔄 获取并验证代理...")
                    else:
                        print(f"🔄 代理周期 {proxy_cycle + 1}/{max_proxy_cycles}: 重新获取代理...")

                    # 获取经过验证的代理
                    current_proxy = self.proxy_manager.get_verified_proxy(max_attempts=5)
                    if current_proxy:
                        print(f"✅ 获取到可用代理: {self.proxy_manager.current_proxy}")
                    else:
                        print("❌ 无法获取可用代理，使用直连模式")
                        current_proxy = None
            else:
                if proxy_cycle == 0:
                    print("🔄 代理未启用，使用直连模式")

            # 在当前代理下进行重试
            for attempt in range(1, max_retries + 1):
                total_attempts += 1

                try:
                    # 检查是否需要更换代理（在当前周期内）
                    if (current_proxy and proxy_attempt_count >= max_proxy_failures and
                        self.config_manager and self.proxy_manager):
                        print(f"🔄 当前代理已重试 {proxy_attempt_count} 次，尝试获取新代理...")
                        # 获取经过验证的新代理
                        new_proxy = self.proxy_manager.get_verified_proxy(max_attempts=3)
                        proxy_attempt_count = 0
                        if new_proxy:
                            current_proxy = new_proxy
                            print(f"✅ 获取到新的可用代理: {self.proxy_manager.current_proxy}")
                        else:
                            print("❌ 无法获取新的可用代理，继续使用当前代理")

                    # 显示当前尝试信息
                    if current_proxy:
                        print(f"周期{proxy_cycle + 1} 尝试{attempt}: 使用代理 {self.proxy_manager.current_proxy} (代理重试 {proxy_attempt_count + 1}/{max_proxy_failures})")
                    else:
                        print(f"周期{proxy_cycle + 1} 尝试{attempt}: 使用直连模式")

                    # 获取验证码图片
                    a, b, cut_path, ori_path = self.get_image_with_proxy(current_proxy)

                    # 识别验证码
                    xcode = self.get_xcode(cut_path, ori_path)

                    # 调整xcode值，可能会提高成功率
                    if total_attempts > 1:
                        adjustment = random.randint(-5, 5)
                        xcode = max(0, xcode + adjustment)

                    # 获取加密参数
                    code = self.get_params(a, b, xcode)

                    # 验证验证码（使用同一个代理）
                    check_result = self.get_check_with_proxy(code, current_proxy)

                    # 如果验证成功，获取数据
                    if check_result.get('msg') == 'success':
                        print(f"周期{proxy_cycle + 1} 尝试{attempt}: 验证码验证成功")
                        success_attempt = total_attempts  # 记录成功的尝试次数

                        result = self.get_data_with_proxy(code, keyword, page_size, current_proxy)
                        if result[0] is not None:
                            data, total_count = result
                            print(f"周期{proxy_cycle + 1} 尝试{attempt}: 数据获取成功，总数: {total_count}")

                            # 标记代理成功，重置失败计数
                            if self.proxy_manager and current_proxy:
                                self.proxy_manager.mark_proxy_success()

                            return data, success_attempt, total_count
                        else:
                            print(f"周期{proxy_cycle + 1} 尝试{attempt}: 验证成功但获取数据失败，继续尝试...")
                            if current_proxy:
                                proxy_attempt_count += 1
                    else:
                        print(f"周期{proxy_cycle + 1} 尝试{attempt}: 验证码验证失败: {check_result}")
                        if current_proxy:
                            proxy_attempt_count += 1

                    # 随机等待一段时间，避免请求过于频繁
                    delay = random.uniform(0.5, 2)
                    time.sleep(delay)

                except Exception as e:
                    print(f"周期{proxy_cycle + 1} 尝试{attempt}: 发生异常: {str(e)[:100]}...")

                    # 检查是否是代理连接错误
                    error_str = str(e).lower()
                    is_proxy_error = any(keyword in error_str for keyword in [
                        'proxy', '503', 'service unavailable', 'tunnel connection failed',
                        'connection refused', 'timeout', 'unable to connect'
                    ])

                    if current_proxy and is_proxy_error:
                        # 代理连接错误，立即切换代理
                        print(f"🔄 检测到代理连接错误，立即获取新代理...")
                        # 标记当前代理失败
                        if self.proxy_manager:
                            self.proxy_manager.mark_proxy_failed()
                        # 获取经过验证的新代理
                        current_proxy = self.proxy_manager.get_verified_proxy(max_attempts=3) if self.proxy_manager else None
                        proxy_attempt_count = 0
                        if current_proxy:
                            print(f"✅ 获取到新的可用代理: {self.proxy_manager.current_proxy}")
                        else:
                            print("❌ 无法获取新的可用代理，继续使用直连模式")
                    elif current_proxy:
                        # 其他错误，增加失败计数
                        proxy_attempt_count += 1
                        print(f"代理失败计数: {proxy_attempt_count}/{max_proxy_failures}")

                    delay = random.uniform(0.5, 2)
                    time.sleep(delay)

            # 当前代理周期结束
            print(f"代理周期 {proxy_cycle + 1} 完成，尝试了 {max_retries} 次")

            # 如果还有剩余周期，稍等一下再开始下一个周期
            if proxy_cycle < max_proxy_cycles - 1:
                print(f"⏳ 等待 3 秒后开始下一个代理周期...")
                time.sleep(3)

        print(f"已完成 {max_proxy_cycles} 个代理周期，总共尝试 {total_attempts} 次，无法获取数据。")
        return None, 0, 0
